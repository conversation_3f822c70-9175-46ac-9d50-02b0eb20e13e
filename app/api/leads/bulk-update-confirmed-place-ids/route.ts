import { NextResponse } from "next/server";
import { query } from "@/lib/db";

interface PlaceIdUpdate {
  leadId: number;
  placeId: string;
  googleName: string | null;
}

interface UpdateBatchInfo {
  batchIndex: number;
  totalBatches: number;
  batchSize: number;
  totalUpdates: number;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { updates, batchInfo } = body as {
      updates: PlaceIdUpdate[];
      batchInfo?: UpdateBatchInfo;
    };

    // Log batch information if provided
    if (batchInfo) {
      console.log(
        `Processing update batch ${batchInfo.batchIndex}/${batchInfo.totalBatches} with ${batchInfo.batchSize} updates (total: ${batchInfo.totalUpdates})`
      );
    }

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { error: "Invalid updates data" },
        { status: 400 }
      );
    }

    if (updates.length === 0) {
      return NextResponse.json(
        { error: "No updates provided" },
        { status: 400 }
      );
    }

    let updatedCount = 0;
    const results = [];

    // Validate all updates first
    const validUpdates = [];
    for (const update of updates) {
      const { leadId, placeId, googleName } = update;

      if (!leadId || !placeId) {
        results.push({
          leadId,
          success: false,
          error: "Missing leadId or placeId",
        });
        continue;
      }

      validUpdates.push({ leadId, placeId, googleName });
    }

    if (validUpdates.length === 0) {
      return NextResponse.json({
        success: true,
        updated: 0,
        total: updates.length,
        results,
        message: "No valid updates to process",
      });
    }

    try {
      // Use bulk update with CASE statement for better performance
      const leadIds = validUpdates.map((u) => u.leadId);
      const placeIdCases = validUpdates
        .map((u, i) => `WHEN ${u.leadId} THEN $${i + 2}`)
        .join(" ");

      const placeIdValues = validUpdates.map((u) => u.placeId);

      // Bulk update query using CASE statement
      const bulkUpdateQuery = `
        UPDATE "bianchi_leads"
        SET google_place_id = CASE id ${placeIdCases} END,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ANY($1::int[])
          AND (google_place_id IS NULL OR google_place_id = '')
        RETURNING id, name, google_place_id
      `;

      const bulkResult = await query(bulkUpdateQuery, [
        leadIds,
        ...placeIdValues,
      ]);

      // Create a map of updated leads for quick lookup
      const updatedLeadsMap = new Map(
        bulkResult.map((lead) => [lead.id, lead])
      );

      // Process results for each update
      for (const update of validUpdates) {
        const updatedLead = updatedLeadsMap.get(update.leadId);

        if (updatedLead) {
          updatedCount++;
          results.push({
            leadId: update.leadId,
            success: true,
            lead: updatedLead,
            googleName: update.googleName,
          });
        } else {
          results.push({
            leadId: update.leadId,
            success: false,
            error: "Lead not found or already has Place ID",
          });
        }
      }
    } catch (error) {
      console.error("Error in bulk update:", error);

      // Fallback to individual updates if bulk update fails
      console.log("Falling back to individual updates...");

      for (const update of validUpdates) {
        try {
          const result = await query(
            `UPDATE "bianchi_leads"
             SET google_place_id = $1, updated_at = CURRENT_TIMESTAMP
             WHERE id = $2 AND (google_place_id IS NULL OR google_place_id = '')
             RETURNING id, name, google_place_id`,
            [update.placeId, update.leadId]
          );

          if (result.length === 0) {
            results.push({
              leadId: update.leadId,
              success: false,
              error: "Lead not found or already has Place ID",
            });
          } else {
            updatedCount++;
            results.push({
              leadId: update.leadId,
              success: true,
              lead: result[0],
              googleName: update.googleName,
            });
          }
        } catch (individualError) {
          console.error(
            `Error updating lead ${update.leadId}:`,
            individualError
          );
          results.push({
            leadId: update.leadId,
            success: false,
            error:
              individualError instanceof Error
                ? individualError.message
                : "Unknown error",
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      updated: updatedCount,
      total: updates.length,
      results,
      message: `Successfully updated ${updatedCount} of ${updates.length} leads`,
    });
  } catch (error) {
    console.error("Error in bulk Place ID update:", error);
    return NextResponse.json(
      {
        error: "Failed to update Place IDs",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
