import { NextResponse } from "next/server";

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchText";

// Delay function to avoid hitting rate limits
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Rate limiter for 600 requests per minute (10 requests per second)
class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests = 600; // 600 requests per minute
  private readonly windowMs = 60 * 1000; // 1 minute window

  async waitForSlot(): Promise<void> {
    const now = Date.now();

    // Remove requests older than the window
    this.requests = this.requests.filter((time) => now - time < this.windowMs);

    // If we're at the limit, wait until we can make another request
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = this.requests[0];
      const waitTime = this.windowMs - (now - oldestRequest) + 10; // Add 10ms buffer
      console.log(`Rate limit reached, waiting ${waitTime}ms`);
      await delay(waitTime);
      return this.waitForSlot(); // Recursive call to check again
    }

    // Record this request
    this.requests.push(now);
  }
}

// Global rate limiter instance
const rateLimiter = new RateLimiter();

// Concurrency control for parallel processing
class ConcurrencyLimiter {
  private running = 0;
  private queue: Array<() => void> = [];

  constructor(private limit: number) {}

  async acquire(): Promise<void> {
    return new Promise((resolve) => {
      if (this.running < this.limit) {
        this.running++;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release(): void {
    this.running--;
    if (this.queue.length > 0) {
      const next = this.queue.shift()!;
      this.running++;
      next();
    }
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    await this.acquire();
    try {
      return await fn();
    } finally {
      this.release();
    }
  }
}

interface LeadWithoutPlaceId {
  id: number;
  name: string;
  formatted_address: string;
  street_name: string;
  street_number: string;
  city: string;
  postal_code: string;
  canton: string;
  country: string;
  international_phone: string | null;
  national_phone: string | null;
  location: {
    latitude: number;
    longitude: number;
  } | null;
}

interface PlaceIdSearchResult {
  leadId: number;
  leadName: string;
  placeId: string | null;
  googleName: string | null;
  confidence: "high" | "medium" | "failed";
  searchQuery: string;
  reason?: string;
}

function constructSearchQuery(lead: LeadWithoutPlaceId): string {
  // Build comprehensive search query using available data
  const parts = [];

  if (lead.name) parts.push(lead.name);

  // Add address components
  if (lead.street_name && lead.street_number) {
    parts.push(`${lead.street_number} ${lead.street_name}`);
  } else if (lead.street_name) {
    parts.push(lead.street_name);
  }

  if (lead.city) parts.push(lead.city);
  if (lead.postal_code) parts.push(lead.postal_code);
  if (lead.canton) parts.push(lead.canton);
  if (lead.country) parts.push(lead.country);

  return parts.filter(Boolean).join(", ");
}

function determineConfidence(hasLocationBias: boolean): "high" | "medium" {
  // Simplified confidence based only on location bias
  // Since Google's algorithm selected this as the best match, we trust it
  if (hasLocationBias) {
    return "high"; // Single result with location bias = high confidence
  } else {
    return "medium"; // Single result without location bias = medium confidence
  }
}

async function searchPlaceId(
  lead: LeadWithoutPlaceId
): Promise<PlaceIdSearchResult> {
  const searchQuery = constructSearchQuery(lead);

  if (!searchQuery) {
    return {
      leadId: lead.id,
      leadName: lead.name,
      placeId: "None", // Set to "None" instead of null for insufficient data
      googleName: null,
      confidence: "failed",
      searchQuery: "",
      reason: "Insufficient data for search query",
    };
  }

  // Retry logic for rate limiting and transient errors
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Prepare request body with location bias if available
      const requestBody: any = {
        textQuery: searchQuery,
        maxResultCount: 1, // Get only the most relevant result
      };

      const hasLocationBias = !!lead.location;

      // Add location bias if coordinates are available
      if (hasLocationBias && lead.location) {
        requestBody.locationBias = {
          circle: {
            center: {
              latitude: lead.location.latitude,
              longitude: lead.location.longitude,
            },
            radius: 1000, // 1km radius
          },
        };
      }

      // Wait for rate limiter before making the request
      await rateLimiter.waitForSlot();

      const response = await fetch(PLACES_API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Goog-Api-Key": GOOGLE_MAPS_API_KEY!,
          // Use only ID Only SKU fields to minimize costs
          "X-Goog-FieldMask": "places.id,nextPageToken",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        // Handle rate limiting with exponential backoff
        if (response.status === 429 && attempt < maxRetries) {
          const backoffDelay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          console.log(
            `Rate limited for lead ${lead.id}, retrying in ${backoffDelay}ms (attempt ${attempt}/${maxRetries})`
          );
          await delay(backoffDelay);
          continue;
        }

        const errorText = await response.text();
        console.error(
          `Places API error for lead ${lead.id} (attempt ${attempt}):`,
          errorText
        );

        if (attempt < maxRetries) {
          lastError = new Error(`API error: ${response.status} - ${errorText}`);
          await delay(1000 * attempt); // Linear backoff for other errors
          continue;
        }

        return {
          leadId: lead.id,
          leadName: lead.name,
          placeId: "None", // Set to "None" instead of null for API errors
          googleName: null,
          confidence: "failed",
          searchQuery,
          reason: `API error: ${response.status}`,
        };
      }

      const data = await response.json();
      const places = data.places || [];

      console.log(
        `\n=== OPTIMIZED SEARCH for "${lead.name}" (attempt ${attempt}) ===`
      );
      console.log(`Search Query: "${searchQuery}"`);
      console.log(`Location Bias: ${hasLocationBias}`);
      console.log(`Found ${places.length} places from API`);

      if (places.length === 0) {
        console.log(`No places found for "${lead.name}"`);
        return {
          leadId: lead.id,
          leadName: lead.name,
          placeId: "None", // Set to "None" instead of null for no results
          googleName: null,
          confidence: "failed",
          searchQuery,
          reason: "No places found",
        };
      }

      // Since we requested maxResultCount: 1, we should have exactly 1 result
      // Trust Google's algorithm - if it returned this result, it's the best match
      const place = places[0];
      const confidence = determineConfidence(hasLocationBias);

      console.log(`Place ID: ${place.id}`);
      console.log(
        `Confidence: ${confidence} (based on location bias: ${hasLocationBias})`
      );

      return {
        leadId: lead.id,
        leadName: lead.name,
        placeId: place.id,
        googleName: null, // Not available in ID Only SKU
        confidence,
        searchQuery,
      };
    } catch (error) {
      console.error(
        `Error searching for lead ${lead.id} (attempt ${attempt}):`,
        error
      );
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt < maxRetries) {
        await delay(1000 * attempt); // Linear backoff for network errors
        continue;
      }
    }
  }

  // If we get here, all retries failed
  return {
    leadId: lead.id,
    leadName: lead.name,
    placeId: "None", // Set to "None" instead of null for search errors
    googleName: null,
    confidence: "failed",
    searchQuery,
    reason: `Search error after ${maxRetries} attempts: ${
      lastError?.message || "Unknown error"
    }`,
  };
}

interface BatchInfo {
  batchIndex: number;
  totalBatches: number;
  batchSize: number;
  totalLeads: number;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { leads, batchInfo } = body as {
      leads: LeadWithoutPlaceId[];
      batchInfo?: BatchInfo;
    };

    if (!leads || !Array.isArray(leads)) {
      return NextResponse.json(
        { error: "Invalid leads data" },
        { status: 400 }
      );
    }

    if (!GOOGLE_MAPS_API_KEY) {
      return NextResponse.json(
        { error: "Google Maps API key not configured" },
        { status: 500 }
      );
    }

    // Log batch information if provided
    if (batchInfo) {
      console.log(
        `Processing batch ${batchInfo.batchIndex}/${batchInfo.totalBatches} with ${batchInfo.batchSize} leads (${leads.length} total leads: ${batchInfo.totalLeads})`
      );
    }

    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        let completed = 0;
        let successful = 0;
        let failed = 0;
        let isStreamClosed = false;

        // Helper function to safely enqueue data
        const safeEnqueue = (data: any): boolean => {
          if (isStreamClosed) {
            // Don't log every skipped enqueue to reduce noise
            return false;
          }

          try {
            controller.enqueue(encoder.encode(JSON.stringify(data) + "\n"));
            return true;
          } catch (error) {
            // Stream was closed by client, mark as closed but don't treat as error
            if (!isStreamClosed) {
              console.log(
                "Stream closed by client, continuing processing in background"
              );
              isStreamClosed = true;
            }
            return false;
          }
        };

        // Helper function to safely close the controller
        const safeClose = () => {
          if (!isStreamClosed) {
            try {
              controller.close();
              isStreamClosed = true;
            } catch (error) {
              // Controller already closed, this is normal
              isStreamClosed = true;
            }
          }
        };

        try {
          // Create concurrency limiter for parallel processing
          // Reduced to 6 concurrent requests to work better with 600/min rate limit
          const concurrencyLimit = 6; // Process up to 6 leads concurrently
          const limiter = new ConcurrencyLimiter(concurrencyLimit);

          console.log(
            `Processing ${leads.length} leads with concurrency limit of ${concurrencyLimit}`
          );

          // Process leads in parallel with controlled concurrency
          const results = await Promise.allSettled(
            leads.map((lead) =>
              limiter.execute(async () => {
                // Search for Place ID - continue processing even if stream is closed
                // Rate limiting is handled by the RateLimiter class in searchPlaceId
                const result = await searchPlaceId(lead);

                // Update counters atomically (always update, regardless of stream state)
                completed++;
                if (result.placeId && result.placeId !== "None") {
                  successful++;
                } else {
                  failed++;
                }

                // Try to send progress update, but don't fail if stream is closed
                const progressData = {
                  type: "progress",
                  completed,
                  successful,
                  failed,
                  total: leads.length,
                };

                safeEnqueue(progressData); // Don't check return value, just try to send

                // Try to send result, but don't fail if stream is closed
                const resultData = {
                  type: "result",
                  result,
                };

                safeEnqueue(resultData); // Don't check return value, just try to send

                return result;
              })
            )
          );

          // Process any rejected promises (errors) - filter out stream closure errors
          const actualErrors = results
            .filter(
              (result): result is PromiseRejectedResult =>
                result.status === "rejected"
            )
            .map((result) => result.reason)
            .filter((error) => {
              // Filter out stream closure errors as these are not actual processing failures
              const errorMessage =
                error instanceof Error ? error.message : String(error);
              return !errorMessage.includes("Stream closed");
            });

          if (actualErrors.length > 0) {
            console.log(
              `${actualErrors.length} leads failed during parallel processing (excluding stream closure):`,
              actualErrors
            );
          }

          // Count successful results from Promise.allSettled
          const successfulResults = results.filter(
            (result) => result.status === "fulfilled"
          ).length;

          console.log(
            `Parallel processing completed: ${successfulResults} promises fulfilled, ${completed} total processed, ${successful} successful, ${failed} failed`
          );

          // Always try to send completion signal, even if stream was closed during processing
          const completeData = {
            type: "complete",
            totalProcessed: completed,
            totalFound: successful,
            totalFailed: failed,
          };
          safeEnqueue(completeData);

          // Log final results regardless of stream state for debugging
          console.log(
            `Bulk search completed: ${completed} processed, ${successful} successful, ${failed} failed (stream was ${
              isStreamClosed ? "closed" : "open"
            })`
          );
        } catch (error) {
          console.error("Error in bulk search stream:", error);

          // Try to send error signal
          const errorData = {
            type: "error",
            message: error instanceof Error ? error.message : "Unknown error",
          };
          safeEnqueue(errorData);
        } finally {
          safeClose();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    console.error("Error in bulk Place ID search:", error);
    return NextResponse.json(
      {
        error: "Failed to process bulk search",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
