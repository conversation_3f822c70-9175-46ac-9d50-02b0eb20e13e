import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Interfaces match the structure used in the frontend/other API for consistency
interface SearchPoint {
  lat: number;
  lng: number;
  radius: number; // Note: Radius is not used directly in the legacy API call when rankby=distance
}

interface ExtensiveSearchRequest {
  points: SearchPoint[];
  type: string;
  keyword?: string;
  maxResultsPerPoint?: number; // Ignored by legacy API pagination, fixed at 20 per page
  includeResultsPerPoint?: boolean; // Flag to include results count per point
}

// Interface for the legacy Nearby Search API response structure
interface LegacyPlaceResult {
  place_id: string;
  name: string;
  vicinity?: string;
  formatted_address?: string; // Sometimes available, use if vicinity is not
  business_status?: "OPERATIONAL" | "CLOSED_TEMPORARILY" | "CLOSED_PERMANENTLY";
  geometry?: {
    location: {
      lat: number;
      lng: number;
    };
  };
  types?: string[];
  // Other fields like photos, rating, etc. exist but are not mapped for now
}

interface LegacyApiResponse {
  results: LegacyPlaceResult[];
  status: string; // e.g., "OK", "ZERO_RESULTS", "INVALID_REQUEST"
  next_page_token?: string;
  error_message?: string; // Included on error
}

// Frontend PlaceResult interface (for mapping)
interface PlaceResult {
  place_id: string;
  name: string;
  vicinity: string;
  formatted_address: string;
  business_status?: string;
  location?: {
    latitude: number;
    longitude: number;
  } | null; // Allow null to match the new API
  types?: string[];
  maps_url?: string;
  canton?: string; // Not available from legacy nearby search
  primary_type?: string;
  street_name?: string; // Not available from legacy nearby search
  postal_code?: string; // Not available from legacy nearby search
  city?: string; // Not available from legacy nearby search
  country?: string; // Not available from legacy nearby search
}

// Constants
const LEGACY_PLACES_API_URL =
  "https://maps.googleapis.com/maps/api/place/nearbysearch/json";
const PAGINATION_DELAY_MS = 200; // Reduced to 1/10 of original delay (200ms instead of 2000ms)
const MAX_PAGES = 3; // Fetch up to 3 pages (initial + 2 paginated) = 60 results max

// Helper function to delay execution
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Rate limiter for 600 requests per minute (10 requests per second)
class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests = 600; // 600 requests per minute
  private readonly windowMs = 60 * 1000; // 1 minute window

  async waitForSlot(): Promise<void> {
    const now = Date.now();

    // Remove requests older than the window
    this.requests = this.requests.filter((time) => now - time < this.windowMs);

    // If we're at the limit, wait until we can make another request
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = this.requests[0];
      const waitTime = this.windowMs - (now - oldestRequest) + 10; // Add 10ms buffer
      console.log(`Rate limit reached, waiting ${waitTime}ms`);
      await delay(waitTime);
      return this.waitForSlot(); // Recursive call to check again
    }

    // Record this request
    this.requests.push(now);
  }
}

// Global rate limiter instance
const rateLimiter = new RateLimiter();

// Concurrency control for parallel processing
class ConcurrencyLimiter {
  private running = 0;
  private queue: Array<() => void> = [];

  constructor(private limit: number) {}

  async acquire(): Promise<void> {
    return new Promise((resolve) => {
      if (this.running < this.limit) {
        this.running++;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release(): void {
    this.running--;
    if (this.queue.length > 0) {
      const next = this.queue.shift()!;
      this.running++;
      next();
    }
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    await this.acquire();
    try {
      return await fn();
    } finally {
      this.release();
    }
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body: ExtensiveSearchRequest = await req.json();
    const { points, type, keyword, includeResultsPerPoint = false } = body;

    console.log("=== LEGACY EXTENSIVE SEARCH REQUEST ===");
    console.log(`Points count: ${points.length}`);
    console.log(`Type: ${type}`);
    console.log(`Keyword: ${keyword || "none"}`);
    console.log(`Include results per point: ${includeResultsPerPoint}`);
    console.log("Sample points:", points.slice(0, 3));

    if (!points || !Array.isArray(points) || points.length === 0) {
      console.error("Invalid search points provided");
      return NextResponse.json(
        { error: "Invalid search points" },
        { status: 400 }
      );
    }

    // Legacy API requires type OR keyword OR name when rankby=distance
    if (!type && !keyword) {
      console.error(
        "Legacy API with rankby=distance requires 'type' or 'keyword'"
      );
      return NextResponse.json(
        {
          error:
            "Legacy search requires 'type' or 'keyword' when ranking by distance",
        },
        { status: 400 }
      );
    }

    const uniqueResults = new Map<string, PlaceResult>();
    const failures: { point: { lat: number; lng: number }; error: string }[] =
      [];
    const resultsPerPoint: number[] = []; // Track results count per point
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      console.error("Google Maps API key not configured");
      return NextResponse.json(
        { error: "Google Maps API key not configured" },
        { status: 500 }
      );
    }

    console.log(
      "Starting to process search points with legacy API (pagination)..."
    );

    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      let currentPage = 1;
      let nextPageToken: string | undefined = undefined;
      let pointTotalResults = 0;

      console.log(
        `Processing point ${i + 1}/${points.length} via Legacy API: lat=${
          point.lat
        }, lng=${point.lng}`
      ); // Radius not sent

      do {
        try {
          const params = new URLSearchParams({
            location: `${point.lat},${point.lng}`,
            key: apiKey,
            rankby: "distance", // Use distance ranking - requires keyword or type, radius is disallowed
          });

          // Add type OR keyword (legacy API preference)
          if (type) {
            params.append("type", type);
          }
          if (keyword) {
            params.append("keyword", keyword);
          }

          let apiUrl: string;
          if (nextPageToken) {
            // For subsequent pages, only pagetoken and key are needed
            const pageParams = new URLSearchParams({
              pagetoken: nextPageToken,
              key: apiKey,
            });
            console.log(
              `Legacy API Request for point ${
                i + 1
              }, Page ${currentPage} (token): ${LEGACY_PLACES_API_URL}?${pageParams.toString()}`
            );
            apiUrl = `${LEGACY_PLACES_API_URL}?${pageParams.toString()}`;
          } else {
            // First page request
            console.log(
              `Legacy API Request for point ${
                i + 1
              }, Page ${currentPage}: ${LEGACY_PLACES_API_URL}?${params.toString()}`
            );
            apiUrl = `${LEGACY_PLACES_API_URL}?${params.toString()}`;
          }

          const response = await fetch(apiUrl);
          const data: LegacyApiResponse = await response.json();

          console.log(
            `Legacy API Response status (Page ${currentPage}): ${data.status}`
          );

          if (data.status !== "OK" && data.status !== "ZERO_RESULTS") {
            console.error(
              `Legacy API error on page ${currentPage}: ${data.status}`,
              data.error_message || "No error message provided."
            );
            if (currentPage === 1) {
              failures.push({
                point: { lat: point.lat, lng: point.lng },
                error: `Legacy API returned ${data.status}: ${
                  data.error_message || "Unknown error"
                }`,
              });

              // If this is the first page and it failed, we won't get any results for this point
              if (pointTotalResults === 0) {
                resultsPerPoint.push(0); // Add a zero for the failed point
              }
            } else {
              console.warn(
                `Pagination failed for point ${
                  i + 1
                } on page ${currentPage}. Proceeding with results from previous pages.`
              );
            }
            nextPageToken = undefined; // Stop pagination for this point
            break; // Exit loop for this point
          }

          const resultsCount = data.results?.length || 0;
          console.log(`Results count (Page ${currentPage}): ${resultsCount}`);
          pointTotalResults += resultsCount;

          // Process results
          if (data.results && Array.isArray(data.results)) {
            // Helper function to extract address components from formatted address
            function extractAddressComponentsFromFormatted(
              formattedAddress: string | undefined
            ): {
              street_name: string;
              city: string;
              postal_code: string;
              canton: string;
              country: string;
            } {
              if (!formattedAddress) {
                return {
                  street_name: "",
                  city: "",
                  postal_code: "",
                  canton: "",
                  country: "Switzerland",
                };
              }

              // Example: "Seestrasse 55, 8700 Küsnacht, Switzerland"
              const parts = formattedAddress
                .split(",")
                .map((part) => part.trim());

              let street_name = "";
              let city = "";
              let postal_code = "";
              let canton = "";
              let country = "Switzerland";

              if (parts.length >= 1) {
                // Extract street name (remove street number if present)
                const streetParts = parts[0].split(" ");
                if (
                  streetParts.length > 1 &&
                  /^\d+[a-zA-Z]?$/.test(streetParts[streetParts.length - 1])
                ) {
                  // Last part is a number, likely the street number
                  street_name = streetParts.slice(0, -1).join(" ");
                } else {
                  street_name = parts[0];
                }
              }

              if (parts.length >= 2) {
                // Extract postal code and city
                const cityParts = parts[1].split(" ");
                if (cityParts.length > 1 && /^\d+$/.test(cityParts[0])) {
                  // First part is a number, likely the postal code
                  postal_code = cityParts[0];
                  city = cityParts.slice(1).join(" ");
                } else {
                  city = parts[1];
                }
              }

              if (parts.length >= 3) {
                // Last part is usually the country
                country = parts[parts.length - 1];

                // If there are more than 3 parts, the third part might be the canton
                if (parts.length > 3) {
                  canton = parts[2];
                } else if (city) {
                  // Try to determine canton from city
                  // This is a simplified approach - in a real app, you'd use a lookup table
                  const cityToCanton: Record<string, string> = {
                    Zürich: "ZH",
                    Zurich: "ZH",
                    Bern: "BE",
                    Genève: "GE",
                    Geneva: "GE",
                    Basel: "BS",
                    Lausanne: "VD",
                    Winterthur: "ZH",
                    Lucerne: "LU",
                    "St. Gallen": "SG",
                    Lugano: "TI",
                    Biel: "BE",
                    Thun: "BE",
                    Köniz: "BE",
                    "La Chaux-de-Fonds": "NE",
                    Fribourg: "FR",
                    Schaffhausen: "SH",
                    Chur: "GR",
                    Vernier: "GE",
                    Uster: "ZH",
                    Sion: "VS",
                    Lancy: "GE",
                    Emmen: "LU",
                    "Yverdon-les-Bains": "VD",
                    Zug: "ZG",
                    Kriens: "LU",
                    "Rapperswil-Jona": "SG",
                    Dübendorf: "ZH",
                    Montreux: "VD",
                    Dietikon: "ZH",
                    Frauenfeld: "TG",
                    Wetzikon: "ZH",
                    Baar: "ZG",
                    Meyrin: "GE",
                    Wädenswil: "ZH",
                    Küsnacht: "ZH",
                    Thalwil: "ZH",
                    Herrliberg: "ZH",
                    Rüschlikon: "ZH",
                    Erlenbach: "ZH",
                    Weiningen: "ZH",
                  };

                  canton = cityToCanton[city] || "";
                }
              }

              return {
                street_name,
                city,
                postal_code,
                canton,
                country,
              };
            }

            const initialSize = uniqueResults.size;
            data.results.forEach((legacyPlace: LegacyPlaceResult) => {
              if (
                legacyPlace.place_id &&
                !uniqueResults.has(legacyPlace.place_id)
              ) {
                // Extract address components from formatted address
                const formattedAddress =
                  legacyPlace.formatted_address || legacyPlace.vicinity || "";
                const addressComponents =
                  extractAddressComponentsFromFormatted(formattedAddress);

                // Map LegacyPlaceResult to PlaceResult
                const mappedResult: PlaceResult = {
                  place_id: legacyPlace.place_id,
                  name: legacyPlace.name || "Unknown",
                  // Use vicinity, fallback to formatted_address if vicinity is missing
                  vicinity:
                    legacyPlace.vicinity ||
                    legacyPlace.formatted_address?.split(",")[0] ||
                    "",
                  formatted_address: formattedAddress,
                  business_status: legacyPlace.business_status || "UNKNOWN",
                  location: legacyPlace.geometry?.location
                    ? {
                        latitude: legacyPlace.geometry.location.lat,
                        longitude: legacyPlace.geometry.location.lng,
                      }
                    : null, // Use null instead of undefined to match the new API
                  types: legacyPlace.types || [],
                  maps_url: `https://www.google.com/maps/place/?q=place_id:${legacyPlace.place_id}`,
                  // Include extracted address components
                  primary_type: legacyPlace.types?.[0] || undefined,
                  street_name: addressComponents.street_name,
                  city: addressComponents.city,
                  postal_code: addressComponents.postal_code,
                  canton: addressComponents.canton,
                  country: addressComponents.country,
                };

                // Log the extracted address components for debugging
                if (currentPage === 1 && uniqueResults.size === initialSize) {
                  console.log("Sample extracted address components:", {
                    formattedAddress,
                    extractedComponents: addressComponents,
                  });
                }

                uniqueResults.set(legacyPlace.place_id, mappedResult);
              }
            });
            console.log(
              `Point ${i + 1}, Page ${currentPage} added ${
                uniqueResults.size - initialSize
              } new unique results. Total unique: ${uniqueResults.size}`
            );
          }

          // Prepare for next page
          nextPageToken = data.next_page_token;
          currentPage++;

          if (nextPageToken && currentPage > MAX_PAGES) {
            console.info(
              `Point ${i + 1}: More than ${
                MAX_PAGES * 20
              } results available (legacy API), but only fetching the first ${
                MAX_PAGES * 20
              }.`
            );
            nextPageToken = undefined; // Stop fetching
          }

          // Add delay before fetching the next page if needed
          if (nextPageToken) {
            console.log(
              `Found next_page_token. Waiting ${PAGINATION_DELAY_MS}ms before fetching page ${currentPage}...`
            );
            await delay(PAGINATION_DELAY_MS);
          }
        } catch (error) {
          console.error(
            `Error processing point ${i + 1}, page ${currentPage} (Legacy):`,
            error
          );
          if (currentPage === 1) {
            failures.push({
              point: { lat: point.lat, lng: point.lng },
              error:
                error instanceof Error
                  ? error.message
                  : "Unknown processing error",
            });
          } else {
            console.warn(
              `Processing error for point ${
                i + 1
              } on page ${currentPage}. Proceeding with results from previous pages.`
            );
          }
          nextPageToken = undefined; // Stop pagination on error
          break; // Exit loop for this point
        }
      } while (nextPageToken && currentPage <= MAX_PAGES);

      console.log(
        `Finished processing point ${
          i + 1
        } (Legacy). Total results for this point: ${pointTotalResults}`
      );

      // Log high-yield search points (20+ results) to database
      if (pointTotalResults >= 20) {
        try {
          await fetch("/api/logs", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              action: "HIGH_YIELD_SEARCH_POINT",
              description: `High-yield search point found: ${pointTotalResults} results at lat=${point.lat}, lng=${point.lng}, radius=${point.radius}m`,
              metadata: {
                latitude: point.lat,
                longitude: point.lng,
                radius: point.radius,
                resultsCount: pointTotalResults,
                searchType: type,
                keyword: keyword || null,
                apiType: "legacy_places_api",
                pointIndex: i + 1,
                totalPoints: points.length,
                pagesProcessed: currentPage - 1,
              },
            }),
          });
          console.log(
            `Logged high-yield point: ${pointTotalResults} results at lat=${point.lat}, lng=${point.lng}`
          );
        } catch (logError) {
          console.error("Failed to log high-yield search point:", logError);
          // Don't throw error - logging failure shouldn't stop the search
        }
      }

      // Store the total results for this point
      resultsPerPoint.push(pointTotalResults);

      // Add a small delay between *points* to avoid hitting overall API rate limits
      if (i < points.length - 1) {
        console.log("Adding short delay before next point (Legacy)...");
        await delay(20); // Reduced to 1/10 of original delay (20ms)
      }
    }

    console.log("=== LEGACY EXTENSIVE SEARCH COMPLETE ===");
    console.log(`Total unique results found: ${uniqueResults.size}`);
    console.log(`Points with failures: ${failures.length}`);
    if (failures.length > 0) {
      console.warn(
        "Failures (Legacy):",
        failures.map((f) => ({ point: f.point, error: f.error }))
      );
    }

    // Calculate statistics for results per point
    let maxResultsForPoint = 0;
    let avgResultsPerPoint = 0;

    if (resultsPerPoint.length > 0) {
      maxResultsForPoint = Math.max(...resultsPerPoint);
      avgResultsPerPoint =
        resultsPerPoint.reduce((sum, count) => sum + count, 0) /
        resultsPerPoint.length;

      console.log("=== RESULTS PER POINT STATISTICS (LEGACY) ===");
      console.log(`Maximum results for any point: ${maxResultsForPoint}`);
      console.log(
        `Average results per point: ${avgResultsPerPoint.toFixed(2)}`
      );
    }

    // Prepare the response
    const response: any = {
      results: Array.from(uniqueResults.values()),
      failures,
      total: uniqueResults.size,
      searchPointsCount: points.length,
    };

    // Include results per point statistics if requested
    if (includeResultsPerPoint) {
      response.resultsPerPoint = resultsPerPoint;
      response.maxResultsForPoint = maxResultsForPoint;
      response.avgResultsPerPoint = avgResultsPerPoint;
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Unhandled API error (Legacy):", error);
    return NextResponse.json(
      { error: "Failed to execute legacy extensive search" },
      { status: 500 }
    );
  }
}
