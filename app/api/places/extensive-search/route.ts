import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { updateProgressData } from "../progress-utils";

interface SearchPoint {
  lat: number;
  lng: number;
  radius: number;
}

interface ExtensiveSearchRequest {
  points: SearchPoint[];
  type: string;
  keyword?: string;
  maxResultsPerPoint: number;
  includeResultsPerPoint?: boolean;
  batchSize?: number;
  includeProgressUpdates?: boolean;
}

interface PlacesRequestBody {
  locationRestriction: {
    circle: {
      center: {
        latitude: number;
        longitude: number;
      };
      radius: number;
    };
  };
  includedTypes: string[];
  maxResultCount: number;
  languageCode: string;
  textQuery?: string;
}

interface PlaceResponse {
  id: string;
  displayName?: { text: string };
  formattedAddress?: string;
  businessStatus?: string;
  location?: { latitude: number; longitude: number };
  types?: string[];
  addressComponents?: any[];
  primaryTypeDisplayName?: { text: string };
  googleMapsUri?: string;
}

// Constants
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchNearby";

// Helper function to delay execution
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Rate limiter for 600 requests per minute (10 requests per second)
class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests = 600; // 600 requests per minute
  private readonly windowMs = 60 * 1000; // 1 minute window

  async waitForSlot(): Promise<void> {
    const now = Date.now();

    // Remove requests older than the window
    this.requests = this.requests.filter((time) => now - time < this.windowMs);

    // If we're at the limit, wait until we can make another request
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = this.requests[0];
      const waitTime = this.windowMs - (now - oldestRequest) + 10; // Add 10ms buffer
      console.log(`Rate limit reached, waiting ${waitTime}ms`);
      await delay(waitTime);
      return this.waitForSlot(); // Recursive call to check again
    }

    // Record this request
    this.requests.push(now);
  }
}

// Global rate limiter instance
const rateLimiter = new RateLimiter();

// Concurrency control for parallel processing
class ConcurrencyLimiter {
  private running = 0;
  private queue: Array<() => void> = [];

  constructor(private limit: number) {}

  async acquire(): Promise<void> {
    return new Promise((resolve) => {
      if (this.running < this.limit) {
        this.running++;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release(): void {
    this.running--;
    if (this.queue.length > 0) {
      const next = this.queue.shift()!;
      this.running++;
      next();
    }
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    await this.acquire();
    try {
      return await fn();
    } finally {
      this.release();
    }
  }
}

// Helper function to extract address components
function findAddressComponent(components: any[], type: string) {
  return components?.find((component) => component.types.includes(type));
}

function extractAddressData(addressComponents: any[]) {
  if (!addressComponents || !Array.isArray(addressComponents)) {
    return {
      street_name: "",
      city: "",
      postal_code: "",
      canton: "",
      country: "Switzerland",
    };
  }

  // Get street components
  const streetName =
    findAddressComponent(addressComponents, "route")?.longText || "";
  const city =
    findAddressComponent(addressComponents, "locality")?.longText || "";
  const postalCode =
    findAddressComponent(addressComponents, "postal_code")?.longText || "";
  const canton =
    findAddressComponent(addressComponents, "administrative_area_level_1")
      ?.shortText || "";
  const country =
    findAddressComponent(addressComponents, "country")?.longText ||
    "Switzerland";

  return {
    street_name: streetName,
    city,
    postal_code: postalCode,
    canton,
    country,
  };
}

// Process a single point
async function processPoint(
  point: SearchPoint,
  pointIndex: number,
  totalPoints: number,
  type: string,
  keyword: string | undefined,
  maxResultsPerPoint: number,
  apiKey: string,
  uniqueResults: Map<string, any>
): Promise<number> {
  try {
    // Prepare request body for new Places API
    const requestBody: PlacesRequestBody = {
      locationRestriction: {
        circle: {
          center: {
            latitude: point.lat,
            longitude: point.lng,
          },
          radius: point.radius,
        },
      },
      includedTypes: [type],
      maxResultCount: maxResultsPerPoint,
      languageCode: "en",
    };

    // Add text query if keyword is provided
    if (keyword) {
      requestBody.textQuery = keyword;
    }

    console.log(
      `API Request for point ${pointIndex + 1}/${totalPoints}:`,
      JSON.stringify(requestBody)
    );

    // Wait for rate limiter before making the request
    await rateLimiter.waitForSlot();

    // Make the request to Places API V1
    const response = await fetch(PLACES_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": apiKey,
        "X-Goog-FieldMask": [
          "places.id",
          "places.displayName",
          "places.formattedAddress",
          "places.businessStatus",
          "places.location",
          "places.types",
          "places.googleMapsUri",
          "places.addressComponents",
          "places.primaryTypeDisplayName",
        ].join(","),
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `API error: ${response.status} ${response.statusText}`,
        errorText
      );
      throw new Error(
        `API returned ${response.status}: ${response.statusText}. ${errorText}`
      );
    }

    const data = await response.json();
    console.log(
      `API Response status: ${response.status} ${response.statusText}`
    );

    // Track the number of results for this point
    const pointResultsCount = data.places?.length || 0;
    console.log(`Results count: ${pointResultsCount}`);

    // Log high-yield search points (20+ results) to database
    if (pointResultsCount >= 20) {
      try {
        await fetch("/api/logs", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            action: "HIGH_YIELD_SEARCH_POINT",
            description: `High-yield search point found: ${pointResultsCount} results at lat=${point.lat}, lng=${point.lng}, radius=${point.radius}m`,
            metadata: {
              latitude: point.lat,
              longitude: point.lng,
              radius: point.radius,
              resultsCount: pointResultsCount,
              searchType: type,
              keyword: keyword || null,
              apiType: "new_places_api",
              pointIndex: pointIndex + 1,
              totalPoints: totalPoints,
            },
          }),
        });
        console.log(
          `Logged high-yield point: ${pointResultsCount} results at lat=${point.lat}, lng=${point.lng}`
        );
      } catch (logError) {
        console.error("Failed to log high-yield search point:", logError);
        // Don't throw error - logging failure shouldn't stop the search
      }
    }

    // Process results, deduplicating by place_id
    if (data.places && Array.isArray(data.places)) {
      const initialSize = uniqueResults.size;
      data.places.forEach((place: PlaceResponse) => {
        if (!uniqueResults.has(place.id)) {
          const addressData = extractAddressData(place.addressComponents || []);

          uniqueResults.set(place.id, {
            place_id: place.id,
            name: place.displayName?.text || "Unknown",
            vicinity: place.formattedAddress?.split(",")[0] || "",
            formatted_address: place.formattedAddress || "",
            business_status: place.businessStatus || "UNKNOWN",
            location: place.location
              ? {
                  latitude: place.location.latitude || 0,
                  longitude: place.location.longitude || 0,
                }
              : null,
            types: place.types || [],
            maps_url:
              place.googleMapsUri ||
              `https://www.google.com/maps/place/?q=place_id:${place.id}`,
            // Extract address components
            street_name: addressData.street_name,
            city: addressData.city,
            postal_code: addressData.postal_code,
            canton: addressData.canton,
            country: addressData.country,
            primary_type:
              place.primaryTypeDisplayName?.text ||
              place.types?.[0] ||
              "unknown",
          });
        }
      });

      console.log(
        `Point ${pointIndex + 1}/${totalPoints} added ${
          uniqueResults.size - initialSize
        } results, total unique results: ${uniqueResults.size}`
      );
    }

    return pointResultsCount;
  } catch (error) {
    console.error(
      `Error processing point ${pointIndex + 1}/${totalPoints}:`,
      error
    );
    throw error;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body: ExtensiveSearchRequest = await req.json();
    const {
      points,
      type,
      keyword,
      maxResultsPerPoint = 20,
      includeResultsPerPoint = false,
      includeProgressUpdates = true,
    } = body;

    console.log("=== EXTENSIVE SEARCH REQUEST ===");
    console.log(`Points count: ${points.length}`);
    console.log(`Type: ${type}`);
    console.log(`Keyword: ${keyword || "none"}`);
    console.log(`Max results per point: ${maxResultsPerPoint}`);
    console.log(`Include results per point: ${includeResultsPerPoint}`);
    console.log(`Include progress updates: ${includeProgressUpdates}`);
    console.log("Sample points:", points.slice(0, 3));

    if (!points || !Array.isArray(points) || points.length === 0) {
      console.error("Invalid search points provided");
      return NextResponse.json(
        { error: "Invalid search points" },
        { status: 400 }
      );
    }

    if (!type) {
      console.error("Place type is required");
      return NextResponse.json(
        { error: "Place type is required" },
        { status: 400 }
      );
    }

    // Track unique results to avoid duplicates
    const uniqueResults = new Map<string, any>();
    const failures: Array<{ point: SearchPoint; error: string }> = [];
    const resultsPerPoint: number[] = [];
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      console.error("Google Maps API key not configured");
      return NextResponse.json(
        { error: "Google Maps API key not configured" },
        { status: 500 }
      );
    }

    console.log(
      "Starting to process search points with parallel processing..."
    );

    const totalPoints = points.length;

    // Set initial progress to 5% to show that something is happening
    updateProgressData(
      5, // progress
      Math.min(6, totalPoints), // in progress - up to 6 concurrent requests
      0, // completed
      totalPoints, // total
      req.headers.get("x-search-id") || "default_search"
    );
    console.log(
      `Initialized search with ${totalPoints} points. Setting initial progress to 5%.`
    );

    // Create concurrency limiter for parallel processing
    const concurrencyLimit = 6; // Process up to 6 points concurrently
    const limiter = new ConcurrencyLimiter(concurrencyLimit);

    console.log(
      `Processing ${totalPoints} points with concurrency limit of ${concurrencyLimit}`
    );

    // Process all points in parallel with controlled concurrency
    const pointResults = await Promise.allSettled(
      points.map((point, pointIndex) =>
        limiter.execute(async () => {
          const pointResultsCount = await processPoint(
            point,
            pointIndex,
            totalPoints,
            type,
            keyword,
            maxResultsPerPoint,
            apiKey,
            uniqueResults
          );

          return { pointIndex, pointResultsCount };
        })
      )
    );

    // Process results and update counters
    let completedPoints = 0;
    const pointResultsCounts: number[] = new Array(totalPoints).fill(0);

    pointResults.forEach((result, index) => {
      if (result.status === "fulfilled") {
        completedPoints++;
        pointResultsCounts[result.value.pointIndex] =
          result.value.pointResultsCount;
      } else {
        console.error(`Point ${index + 1} failed:`, result.reason);
        failures.push({
          point: points[index],
          error:
            result.reason instanceof Error
              ? result.reason.message
              : "Unknown error",
        });
        pointResultsCounts[index] = 0;
      }
    });

    // Add all point results to resultsPerPoint array
    resultsPerPoint.push(...pointResultsCounts);

    // Convert the Map to an array of results
    const results = Array.from(uniqueResults.values());

    console.log("=== EXTENSIVE SEARCH COMPLETED ===");
    console.log(`Total points processed: ${points.length}`);
    console.log(`Total unique results: ${results.length}`);
    console.log(`Failures: ${failures.length}`);

    if (failures.length > 0) {
      console.log("Sample failures:", failures.slice(0, 3));
    }

    if (results.length > 0) {
      console.log("Sample results:", results.slice(0, 3));
    }

    // Calculate statistics for results per point
    let maxResultsForPoint = 0;
    let avgResultsPerPoint = 0;

    if (resultsPerPoint.length > 0) {
      maxResultsForPoint = Math.max(...resultsPerPoint);
      avgResultsPerPoint =
        resultsPerPoint.reduce((sum: number, count: number) => sum + count, 0) /
        resultsPerPoint.length;

      console.log("=== RESULTS PER POINT STATISTICS ===");
      console.log(`Maximum results for any point: ${maxResultsForPoint}`);
      console.log(
        `Average results per point: ${avgResultsPerPoint.toFixed(2)}`
      );
    }

    // Update progress data to 100% complete
    updateProgressData(
      100, // progress
      0, // in progress
      totalPoints, // completed
      totalPoints, // total
      req.headers.get("x-search-id") || "default_search"
    );

    // Log final progress update
    console.log(
      `Updated server-side progress to 100% complete. Total points processed: ${totalPoints}`
    );

    // Prepare the response
    const response = {
      results,
      total: results.length,
      failures: failures.length > 0 ? failures : undefined,
      searchPointsCount: points.length,
      progress: 100, // Search is complete, so progress is 100%
    };

    // Include results per point statistics if requested
    if (includeResultsPerPoint) {
      Object.assign(response, {
        resultsPerPoint,
        maxResultsForPoint,
        avgResultsPerPoint,
      });
    }

    // Include detailed progress information if requested
    if (includeProgressUpdates) {
      Object.assign(response, {
        pointsProcessed: totalPoints,
        totalPoints: totalPoints,
        completionTime: new Date().toISOString(),
      });
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in extensive search:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
