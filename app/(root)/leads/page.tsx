"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Lead } from "@/types/lead";
import { createColumns } from "./columns";
import { DataTable, DataTableRef } from "./data-table";
import { logActivity, LogAction } from "@/lib/logger";
import { Button } from "@/components/ui/button";
import {
  Database,
  AlertCircle,
  Filter,
  Table as TableIcon,
  Map,
  Download,
  MoreHorizontal,
  Info,
  X,
  Search,
  ChevronDown,
  ChevronRight,
  Plus,
} from "lucide-react";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import LeadsMap from "@/components/LeadsMap";
import TableSkeleton from "@/components/TableSkeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Too<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Types for pagination
interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface LeadsResponse {
  leads: Lead[];
  pagination?: PaginationInfo;
}

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [allLeads, setAllLeads] = useState<Lead[]>([]); // For map view and export
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<"table" | "map">("table");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterCount, setFilterCount] = useState(0);
  const [expandedDuplicates, setExpandedDuplicates] = useState<Set<string>>(
    new Set()
  );
  const [expandedDuplicateLeads, setExpandedDuplicateLeads] = useState<{
    [placeId: string]: Lead[];
  }>({});

  // Pagination state
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 25,
    total: 0,
    totalPages: 0,
  });

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [filters, setFilters] = useState<{
    canton?: string;
    businessStatus?: string;
    addressGroupDescription?: string;
    source?: string;
    hasPlaceId?: "true" | "false" | "none";
    hasLocation?: "true" | "false";
    approved?: "true" | "false";
    timePeriod?: string;
    showDuplicatesOnly?: "true" | "false";
    postalCode?: string;
    newLeads?: "true" | "false";
  }>({});

  // Filter options from server
  const [filterOptions, setFilterOptions] = useState<{
    cantons: string[];
    businessStatuses: string[];
    addressGroupDescriptions: string[];
    sources: string[];
  }>({
    cantons: [],
    businessStatuses: [],
    addressGroupDescriptions: [],
    sources: [],
  });

  // Duplicates info from server
  const [duplicatesInfo, setDuplicatesInfo] = useState<{
    duplicateGroups: any[];
    totalDuplicateLeads: number;
    duplicateGroupsCount: number;
  }>({
    duplicateGroups: [],
    totalDuplicateLeads: 0,
    duplicateGroupsCount: 0,
  });

  // Reference to the DataTable component
  const dataTableRef = React.useRef<DataTableRef>(null);

  // Function to fetch all duplicate leads for a specific place ID
  const fetchDuplicateLeads = async (placeId: string): Promise<Lead[]> => {
    try {
      const response = await fetch(`/api/leads/duplicates?placeId=${placeId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch duplicate leads");
      }
      const data = await response.json();
      return data.leads || [];
    } catch (error) {
      console.error("Error fetching duplicate leads:", error);
      return [];
    }
  };

  // Fetch leads with pagination
  const fetchLeads = useCallback(
    async (
      page: number = pagination.page,
      pageSize: number = pagination.pageSize,
      search: string = searchTerm,
      sortField: string = sortBy,
      sortDirection: "asc" | "desc" = sortOrder,
      currentFilters = filters
    ) => {
      try {
        setLoading(true);

        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          sortBy: sortField,
          sortOrder: sortDirection,
        });

        if (search) {
          params.append("search", search);
        }

        // Add filter parameters
        if (currentFilters.canton) {
          params.append("canton", currentFilters.canton);
        }
        if (currentFilters.businessStatus) {
          params.append("businessStatus", currentFilters.businessStatus);
        }
        if (currentFilters.addressGroupDescription) {
          params.append(
            "addressGroupDescription",
            currentFilters.addressGroupDescription
          );
        }
        if (currentFilters.source) {
          params.append("source", currentFilters.source);
        }
        if (currentFilters.hasPlaceId) {
          params.append("hasPlaceId", currentFilters.hasPlaceId);
        }
        if (currentFilters.hasLocation) {
          params.append("hasLocation", currentFilters.hasLocation);
        }
        if (currentFilters.approved) {
          params.append("approved", currentFilters.approved);
        }
        if (currentFilters.timePeriod) {
          params.append("timePeriod", currentFilters.timePeriod);
        }
        if (currentFilters.showDuplicatesOnly) {
          params.append(
            "showDuplicatesOnly",
            currentFilters.showDuplicatesOnly
          );
        }
        if (currentFilters.postalCode) {
          params.append("postalCode", currentFilters.postalCode);
        }
        if (currentFilters.newLeads) {
          params.append("newLeads", currentFilters.newLeads);
        }

        const res = await fetch(`/api/leads?${params.toString()}`);

        if (!res.ok) {
          throw new Error(`Failed to fetch leads: ${res.statusText}`);
        }

        const data: LeadsResponse = await res.json();

        if (!data.leads || !Array.isArray(data.leads)) {
          throw new Error(
            "Invalid response format: expected an array of leads"
          );
        }

        setLeads(data.leads);
        if (data.pagination) {
          setPagination(data.pagination);
        }
        setError(null);

        // Log activity without blocking
        logActivity(LogAction.LEAD_EXPORT, "Leads page viewed", {
          leads_count: data.leads.length,
          page,
          pageSize,
          filters: currentFilters,
        }).catch(console.error);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);

        // Log error without blocking
        logActivity(LogAction.LEAD_EXPORT, "Failed to load leads", {
          error: errorMessage,
        }).catch(console.error);
      } finally {
        setLoading(false);
      }
    },
    [
      pagination.page,
      pagination.pageSize,
      searchTerm,
      sortBy,
      sortOrder,
      filters,
    ]
  );

  // Fetch filter options from server
  const fetchFilterOptions = useCallback(async () => {
    try {
      const res = await fetch("/api/leads/filter-options");
      if (!res.ok) {
        throw new Error(`Failed to fetch filter options: ${res.statusText}`);
      }
      const data = await res.json();
      setFilterOptions(data);
    } catch (err) {
      console.error("Failed to fetch filter options:", err);
    }
  }, []);

  // Fetch duplicates info from server
  const fetchDuplicatesInfo = useCallback(async () => {
    try {
      const res = await fetch("/api/leads/duplicates");
      if (!res.ok) {
        throw new Error(`Failed to fetch duplicates info: ${res.statusText}`);
      }
      const data = await res.json();
      setDuplicatesInfo(data);
    } catch (err) {
      console.error("Failed to fetch duplicates info:", err);
    }
  }, []);

  // Fetch all leads for map view and export (backward compatibility)
  const fetchAllLeads = useCallback(async () => {
    try {
      const res = await fetch("/api/leads"); // No pagination params = all leads
      if (!res.ok) {
        throw new Error(`Failed to fetch all leads: ${res.statusText}`);
      }
      const data = await res.json();
      if (data.leads && Array.isArray(data.leads)) {
        setAllLeads(data.leads);
      }
    } catch (err) {
      console.error("Failed to fetch all leads:", err);
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchLeads();
    fetchAllLeads();
    fetchFilterOptions();
    fetchDuplicatesInfo();
  }, [fetchLeads, fetchAllLeads, fetchFilterOptions, fetchDuplicatesInfo]);

  // Refetch when pagination, search, sort, or filters change
  useEffect(() => {
    fetchLeads();
  }, [
    fetchLeads,
    pagination.page,
    pagination.pageSize,
    searchTerm,
    sortBy,
    sortOrder,
    filters,
  ]);

  // Clear expanded duplicate leads when pagination or filters change
  useEffect(() => {
    // Keep expanded state but clear cached leads to force refetch
    setExpandedDuplicateLeads({});
  }, [pagination.page, pagination.pageSize, searchTerm, filters]);

  // Refetch expanded duplicate leads when they are cleared but still in expanded state
  useEffect(() => {
    const refetchExpandedLeads = async () => {
      const missingExpandedLeads: string[] = [];
      expandedDuplicates.forEach((placeId) => {
        if (!expandedDuplicateLeads[placeId]) {
          missingExpandedLeads.push(placeId);
        }
      });

      if (missingExpandedLeads.length > 0) {
        const newExpandedLeads: { [placeId: string]: Lead[] } = {
          ...expandedDuplicateLeads,
        };
        await Promise.all(
          missingExpandedLeads.map(async (placeId) => {
            const duplicateLeads = await fetchDuplicateLeads(placeId);
            newExpandedLeads[placeId] = duplicateLeads;
          })
        );
        setExpandedDuplicateLeads(newExpandedLeads);
      }
    };

    refetchExpandedLeads();
  }, [expandedDuplicates, expandedDuplicateLeads]);

  const handleExport = async () => {
    try {
      // Your existing export code...

      await logActivity(LogAction.LEAD_EXPORT, "Leads exported successfully", {
        format: "excel",
        leads_count: leads.length,
        export_timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Error exporting leads:", error);

      await logActivity(LogAction.LEAD_EXPORT, "Failed to export leads", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  if (loading && leads.length === 0) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col space-y-4">
          {/* Header skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="h-8 w-48 bg-muted animate-pulse rounded" />
            <div className="flex gap-2">
              <div className="h-10 w-32 bg-muted animate-pulse rounded" />
              <div className="h-10 w-24 bg-muted animate-pulse rounded" />
              <div className="h-10 w-24 bg-muted animate-pulse rounded" />
            </div>
          </div>

          {/* Table skeleton */}
          <TableSkeleton rows={10} columns={8} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-6 py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Calculate some statistics for the dashboard cards (use allLeads for accurate totals)
  const getLeadStats = () => {
    const dataToUse = allLeads.length > 0 ? allLeads : leads;
    if (!dataToUse.length)
      return { total: 0, withPlaceId: 0, withLocation: 0, approved: 0 };

    const withPlaceId = dataToUse.filter(
      (lead) => lead.google_place_id && lead.google_place_id !== "None"
    ).length;
    const withLocation = dataToUse.filter((lead) => lead.location).length;
    const approved = dataToUse.filter((lead) => lead.approved).length;

    return {
      total: pagination.total || dataToUse.length,
      withPlaceId,
      withLocation,
      approved,
    };
  };

  const stats = getLeadStats();

  // Process leads to identify duplicates and create display data with ERP-first hierarchy
  const processLeadsWithDuplicates = () => {
    if (!leads.length) return [];

    // Group leads by google_place_id (from current page)
    const leadGroups: { [key: string]: Lead[] } = {};
    const leadsWithoutPlaceId: Lead[] = [];

    leads.forEach((lead) => {
      if (lead.google_place_id && lead.google_place_id !== "None") {
        if (!leadGroups[lead.google_place_id]) {
          leadGroups[lead.google_place_id] = [];
        }
        leadGroups[lead.google_place_id].push(lead);
      } else {
        leadsWithoutPlaceId.push(lead);
      }
    });

    const processedLeads: (Lead & {
      isDuplicate?: boolean;
      isHiddenDuplicate?: boolean;
      duplicateCount?: number;
      duplicateGroupId?: string;
      isErpPrimary?: boolean;
      isPlacesApiSubordinate?: boolean;
    })[] = [];

    // Process groups with duplicates using ERP-first hierarchy
    Object.entries(leadGroups).forEach(([placeId, groupLeads]) => {
      // Check if this place ID has duplicates across the entire dataset
      const duplicateGroup = duplicatesInfo.duplicateGroups.find(
        (g) => g.google_place_id === placeId
      );
      const isDuplicateGroup = !!duplicateGroup;
      const totalDuplicateCount = duplicateGroup
        ? duplicateGroup.count
        : groupLeads.length;

      // Get all duplicate leads for this place ID if expanded
      const allDuplicateLeads = expandedDuplicates.has(placeId)
        ? expandedDuplicateLeads[placeId] || groupLeads
        : groupLeads;

      // Separate expanded leads by source
      const allErpLeads = allDuplicateLeads.filter(
        (lead) => lead.source === "ERP"
      );
      const allPlacesApiLeads = allDuplicateLeads.filter(
        (lead) => lead.source !== "ERP"
      );

      // Sort ERP leads by creation date (newest first)
      const sortedErpLeads = [...allErpLeads].sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      // Sort PlacesAPI leads by creation date (newest first)
      const sortedPlacesApiLeads = [...allPlacesApiLeads].sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      if (sortedErpLeads.length > 0) {
        // ERP lead exists - it becomes the primary expandable lead
        const primaryErpLead = sortedErpLeads[0];
        const otherErpLeads = sortedErpLeads.slice(1);

        // Add primary ERP lead with expansion capability
        processedLeads.push({
          ...primaryErpLead,
          isDuplicate: isDuplicateGroup,
          duplicateCount: totalDuplicateCount,
          duplicateGroupId: placeId,
          isErpPrimary: true,
        });

        // Add other ERP leads as hidden duplicates if expanded
        if (expandedDuplicates.has(placeId)) {
          otherErpLeads.forEach((lead) => {
            processedLeads.push({
              ...lead,
              isDuplicate: isDuplicateGroup,
              isHiddenDuplicate: true,
              duplicateGroupId: placeId,
            });
          });

          // Add PlacesAPI leads as subordinate leads if expanded
          sortedPlacesApiLeads.forEach((lead) => {
            processedLeads.push({
              ...lead,
              isDuplicate: isDuplicateGroup,
              isPlacesApiSubordinate: true,
              duplicateGroupId: placeId,
            });
          });
        }
      } else if (sortedPlacesApiLeads.length > 0) {
        // No ERP lead - treat first PlacesAPI lead as primary
        const primaryPlacesApiLead = sortedPlacesApiLeads[0];
        const otherPlacesApiLeads = sortedPlacesApiLeads.slice(1);

        // Add primary PlacesAPI lead (fallback case)
        processedLeads.push({
          ...primaryPlacesApiLead,
          isDuplicate: isDuplicateGroup,
          duplicateCount: totalDuplicateCount,
          duplicateGroupId: placeId,
        });

        // Add other PlacesAPI leads as hidden duplicates if expanded
        if (expandedDuplicates.has(placeId)) {
          otherPlacesApiLeads.forEach((lead) => {
            processedLeads.push({
              ...lead,
              isDuplicate: isDuplicateGroup,
              isHiddenDuplicate: true,
              duplicateGroupId: placeId,
            });
          });
        }
      }
    });

    // Handle duplicate groups that are expanded but don't have any leads on current page
    expandedDuplicates.forEach((placeId) => {
      if (!leadGroups[placeId] && expandedDuplicateLeads[placeId]) {
        // This duplicate group is expanded but has no leads on current page
        const allDuplicateLeads = expandedDuplicateLeads[placeId];
        const duplicateGroup = duplicatesInfo.duplicateGroups.find(
          (g) => g.google_place_id === placeId
        );
        const totalDuplicateCount = duplicateGroup
          ? duplicateGroup.count
          : allDuplicateLeads.length;

        // Separate ERP and PlacesAPI leads
        const erpLeads = allDuplicateLeads.filter(
          (lead) => lead.source === "ERP"
        );
        const placesApiLeads = allDuplicateLeads.filter(
          (lead) => lead.source !== "ERP"
        );

        // Sort ERP leads by creation date (newest first)
        const sortedErpLeads = [...erpLeads].sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        // Sort PlacesAPI leads by creation date (newest first)
        const sortedPlacesApiLeads = [...placesApiLeads].sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        if (sortedErpLeads.length > 0) {
          // ERP lead exists - it becomes the primary expandable lead
          const primaryErpLead = sortedErpLeads[0];
          const otherErpLeads = sortedErpLeads.slice(1);

          // Add primary ERP lead with expansion capability
          processedLeads.push({
            ...primaryErpLead,
            isDuplicate: true,
            duplicateCount: totalDuplicateCount,
            duplicateGroupId: placeId,
            isErpPrimary: true,
          });

          // Add other ERP leads as hidden duplicates
          otherErpLeads.forEach((lead) => {
            processedLeads.push({
              ...lead,
              isDuplicate: true,
              isHiddenDuplicate: true,
              duplicateGroupId: placeId,
            });
          });

          // Add PlacesAPI leads as subordinate leads
          sortedPlacesApiLeads.forEach((lead) => {
            processedLeads.push({
              ...lead,
              isDuplicate: true,
              isPlacesApiSubordinate: true,
              duplicateGroupId: placeId,
            });
          });
        } else if (sortedPlacesApiLeads.length > 0) {
          // No ERP lead - treat first PlacesAPI lead as primary
          const primaryPlacesApiLead = sortedPlacesApiLeads[0];
          const otherPlacesApiLeads = sortedPlacesApiLeads.slice(1);

          // Add primary PlacesAPI lead (fallback case)
          processedLeads.push({
            ...primaryPlacesApiLead,
            isDuplicate: true,
            duplicateCount: totalDuplicateCount,
            duplicateGroupId: placeId,
          });

          // Add other PlacesAPI leads as hidden duplicates
          otherPlacesApiLeads.forEach((lead) => {
            processedLeads.push({
              ...lead,
              isDuplicate: true,
              isHiddenDuplicate: true,
              duplicateGroupId: placeId,
            });
          });
        }
      }
    });

    // Add leads without place IDs (only if not filtering for duplicates only)
    if (filters.showDuplicatesOnly !== "true") {
      processedLeads.push(...leadsWithoutPlaceId);
    }

    return processedLeads;
  };

  const processedLeads = processLeadsWithDuplicates();

  // Debug: Log duplicate information with hierarchy
  const erpPrimaryLeads = processedLeads.filter((lead) => lead.isErpPrimary);
  const placesApiSubordinates = processedLeads.filter(
    (lead) => lead.isPlacesApiSubordinate
  );

  if (erpPrimaryLeads.length > 0) {
    console.log("ERP Primary leads:", erpPrimaryLeads.length);
    console.log("PlacesAPI subordinate leads:", placesApiSubordinates.length);
    console.log(
      "ERP Primary leads details:",
      erpPrimaryLeads.map((lead) => ({
        name: lead.name,
        source: lead.source,
        google_place_id: lead.google_place_id,
        duplicateCount: lead.duplicateCount,
      }))
    );
  }

  // Toggle duplicate expansion
  const toggleDuplicateExpansion = async (placeId: string) => {
    const isCurrentlyExpanded = expandedDuplicates.has(placeId);

    if (isCurrentlyExpanded) {
      // Collapse: remove from expanded set and clear cached leads
      setExpandedDuplicates((prev) => {
        const newSet = new Set(prev);
        newSet.delete(placeId);
        return newSet;
      });
      setExpandedDuplicateLeads((prev) => {
        const newLeads = { ...prev };
        delete newLeads[placeId];
        return newLeads;
      });
    } else {
      // Expand: add to expanded set and fetch all duplicate leads
      setExpandedDuplicates((prev) => {
        const newSet = new Set(prev);
        newSet.add(placeId);
        return newSet;
      });

      // Fetch all duplicate leads for this place ID
      const duplicateLeads = await fetchDuplicateLeads(placeId);
      setExpandedDuplicateLeads((prev) => ({
        ...prev,
        [placeId]: duplicateLeads,
      }));
    }
  };

  // Expand all duplicates - only expand groups that have ERP leads or are PlacesAPI-only
  const expandAllDuplicates = async () => {
    // Get all duplicate group IDs from server-side duplicate info
    const allDuplicateGroupIds = new Set<string>();
    duplicatesInfo.duplicateGroups.forEach((group) => {
      // Only expand groups that have ERP leads or are PlacesAPI-only groups
      allDuplicateGroupIds.add(group.google_place_id);
    });

    setExpandedDuplicates(allDuplicateGroupIds);

    // Fetch all duplicate leads for each group
    const newExpandedLeads: { [placeId: string]: Lead[] } = {};
    await Promise.all(
      Array.from(allDuplicateGroupIds).map(async (placeId) => {
        const duplicateLeads = await fetchDuplicateLeads(placeId);
        newExpandedLeads[placeId] = duplicateLeads;
      })
    );

    setExpandedDuplicateLeads(newExpandedLeads);
  };

  // Collapse all duplicates
  const collapseAllDuplicates = () => {
    setExpandedDuplicates(new Set());
    setExpandedDuplicateLeads({});
  };

  // Helper to clear all filters
  const clearAllFilters = () => {
    setFilters({});
    setSearchTerm("");
    setPagination((prev) => ({ ...prev, page: 1 }));
    if (dataTableRef.current) {
      dataTableRef.current.clearAllFilters();
    }
  };

  // The existing handleExport function is already defined above

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-4">
        {/* Header with actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-2xl font-bold">Leads Management</h1>

          <div className="flex flex-wrap gap-2">
            {/* View toggle */}
            <Tabs
              value={activeView}
              onValueChange={(value) => setActiveView(value as "table" | "map")}
              className="mr-2"
            >
              <TabsList className="grid w-[180px] grid-cols-2">
                <TabsTrigger value="table" className="flex items-center gap-1">
                  <TableIcon className="h-4 w-4" />
                  <span>Table</span>
                </TabsTrigger>
                <TabsTrigger value="map" className="flex items-center gap-1">
                  <Map className="h-4 w-4" />
                  <span>Map</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Filter toggle */}
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            >
              <Filter className="h-4 w-4" />
              <span>Filters</span>
              {filterCount > 0 && (
                <span className="ml-1 rounded-full bg-primary text-primary-foreground w-5 h-5 text-xs flex items-center justify-center">
                  {filterCount}
                </span>
              )}
            </Button>

            {/* Duplicates filter toggle */}
            <Button
              variant={
                filters.showDuplicatesOnly === "true" ? "default" : "outline"
              }
              className="flex items-center gap-1"
              onClick={() => {
                const newFilters = { ...filters };
                if (filters.showDuplicatesOnly === "true") {
                  delete newFilters.showDuplicatesOnly;
                } else {
                  newFilters.showDuplicatesOnly = "true";
                }
                setFilters(newFilters);
                setPagination((prev) => ({ ...prev, page: 1 }));
              }}
            >
              <AlertCircle className="h-4 w-4" />
              <span>ERP Match</span>
              {filters.showDuplicatesOnly === "true" && (
                <span className="ml-1 rounded-full bg-white text-primary w-5 h-5 text-xs flex items-center justify-center">
                  {duplicatesInfo.duplicateGroupsCount}
                </span>
              )}
            </Button>

            {/* New Leads filter */}
            <Button
              variant={filters.newLeads === "true" ? "default" : "outline"}
              className="flex items-center gap-1"
              onClick={() => {
                const newFilters = { ...filters };
                if (filters.newLeads === "true") {
                  delete newFilters.newLeads;
                } else {
                  newFilters.newLeads = "true";
                }
                setFilters(newFilters);
                setPagination((prev) => ({ ...prev, page: 1 }));
              }}
            >
              <Plus className="h-4 w-4" />
              <span>New Leads</span>
            </Button>

            {/* Actions dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  <MoreHorizontal className="h-4 w-4" />
                  <span>Actions</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Lead Actions</DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link
                    href="/leads/bulk-place-id"
                    className="flex items-center gap-2"
                  >
                    <Search className="h-4 w-4" />
                    Bulk Place ID Assignment
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href="/leads/erp-sync"
                    className="flex items-center gap-2"
                  >
                    <Database className="h-4 w-4" />
                    ERP Sync
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleExport}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export All Leads
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={clearAllFilters}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Clear All Filters
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>Duplicate Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={expandAllDuplicates}
                  className="flex items-center gap-2"
                  disabled={
                    erpPrimaryLeads.length === 0 &&
                    duplicatesInfo.duplicateGroupsCount === 0
                  }
                >
                  <ChevronDown className="h-4 w-4" />
                  Expand All Duplicates
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={collapseAllDuplicates}
                  className="flex items-center gap-2"
                  disabled={expandedDuplicates.size === 0}
                >
                  <ChevronRight className="h-4 w-4" />
                  Collapse All Duplicates
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Stats summary - simplified */}
        {leads.length > 0 && (
          <div className="flex flex-wrap gap-4 mb-4 text-sm text-muted-foreground">
            {filters.showDuplicatesOnly === "true" ? (
              <>
                <span>
                  <strong>{duplicatesInfo.duplicateGroupsCount}</strong>{" "}
                  duplicate groups
                </span>
                <span>
                  <strong>{pagination.total}</strong> total duplicate leads
                </span>
                <span>
                  <strong>{expandedDuplicates.size}</strong> groups expanded
                </span>
              </>
            ) : (
              <>
                <span>
                  <strong>{stats.total}</strong> total leads
                  {pagination.total > 0 &&
                    pagination.total !== leads.length && (
                      <span className="text-muted-foreground">
                        {" "}
                        (showing {leads.length} of {pagination.total})
                      </span>
                    )}
                </span>
                {duplicatesInfo.duplicateGroupsCount > 0 && (
                  <span>
                    <strong>{duplicatesInfo.duplicateGroupsCount}</strong>{" "}
                    duplicate groups
                  </span>
                )}
                {stats.withLocation > 0 && (
                  <span>
                    <strong>{stats.withLocation}</strong> with location data
                  </span>
                )}
                {stats.approved > 0 && (
                  <span>
                    <strong>{stats.approved}</strong> approved
                  </span>
                )}
              </>
            )}
          </div>
        )}

        {/* Color legend */}
        <div className="flex flex-wrap gap-4 mb-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-green-50 border border-green-200"></div>
                  <span className="text-sm">Kunde</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Customer leads are highlighted in green</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-red-50 border border-red-200"></div>
                  <span className="text-sm">Werbekunde</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Advertising customer leads are highlighted in red</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded bg-blue-50 border border-blue-200"></div>
                  <span className="text-sm">No Address Group</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Leads without an address group are highlighted in blue</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm">ERP Primary</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  Red pin indicates ERP leads that can be expanded to show
                  associated PlacesAPI leads. Click to expand/collapse.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                  <span className="text-sm">PlacesAPI Subordinate</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  Blue dot indicates PlacesAPI leads that are subordinate to an
                  ERP lead. These are shown when the ERP lead is expanded.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 gap-1">
                  <Info className="h-3 w-3" />
                  <span className="text-xs">About Colors</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p>
                  Rows are colored based on the Address Group Description field
                  to help you quickly identify different types of leads.
                  Duplicate leads are indicated with a red pin icon and can be
                  expanded to show hidden duplicates.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Collapsible filter panel */}
        <Collapsible
          open={isFilterOpen}
          onOpenChange={setIsFilterOpen}
          className="mb-4"
        >
          <CollapsibleContent className="p-4 border rounded-md bg-muted/10 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Location Filters */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Location Filters</h3>
                <div className="space-y-2">
                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="canton-filter" className="text-xs">
                      Canton
                    </Label>
                    <Select
                      value={filters.canton || "all"}
                      onValueChange={(value) => {
                        const newFilters = { ...filters };
                        if (value === "all") {
                          delete newFilters.canton;
                        } else {
                          newFilters.canton = value;
                        }
                        setFilters(newFilters);
                        setPagination((prev) => ({ ...prev, page: 1 })); // Reset to page 1
                      }}
                    >
                      <SelectTrigger id="canton-filter" className="h-8">
                        <SelectValue placeholder="All Cantons" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Cantons</SelectItem>
                        {filterOptions.cantons.map((canton) => (
                          <SelectItem key={canton} value={canton}>
                            {canton}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="postal-code-filter" className="text-xs">
                      Postal Code
                    </Label>
                    <Input
                      id="postal-code-filter"
                      placeholder="e.g. 8001"
                      className="h-8"
                      value={filters.postalCode || ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        const newFilters = { ...filters };
                        if (value) {
                          newFilters.postalCode = value;
                        } else {
                          delete newFilters.postalCode;
                        }
                        setFilters(newFilters);
                        setPagination((prev) => ({ ...prev, page: 1 }));
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Business Filters */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Business Filters</h3>
                <div className="space-y-2">
                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="business-status-filter" className="text-xs">
                      Business Status
                    </Label>
                    <Select
                      value={filters.businessStatus || "all"}
                      onValueChange={(value) => {
                        const newFilters = { ...filters };
                        if (value === "all") {
                          delete newFilters.businessStatus;
                        } else {
                          newFilters.businessStatus = value;
                        }
                        setFilters(newFilters);
                        setPagination((prev) => ({ ...prev, page: 1 }));
                      }}
                    >
                      <SelectTrigger
                        id="business-status-filter"
                        className="h-8"
                      >
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        {filterOptions.businessStatuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status
                              .replace(/_/g, " ")
                              .toLowerCase()
                              .split(" ")
                              .map(
                                (word) =>
                                  word.charAt(0).toUpperCase() + word.slice(1)
                              )
                              .join(" ")}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="address-group-filter" className="text-xs">
                      Address Group
                    </Label>
                    <Select
                      value={filters.addressGroupDescription || "all"}
                      onValueChange={(value) => {
                        const newFilters = { ...filters };
                        if (value === "all") {
                          delete newFilters.addressGroupDescription;
                        } else {
                          newFilters.addressGroupDescription = value;
                        }
                        setFilters(newFilters);
                        setPagination((prev) => ({ ...prev, page: 1 }));
                      }}
                    >
                      <SelectTrigger id="address-group-filter" className="h-8">
                        <SelectValue placeholder="All Groups" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Groups</SelectItem>
                        <SelectItem value="__empty__">No Group</SelectItem>
                        {filterOptions.addressGroupDescriptions.map((group) => (
                          <SelectItem key={group} value={group}>
                            {group}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Data Quality Filters */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">Data Quality Filters</h3>
                <div className="space-y-2">
                  <div className="flex flex-col gap-1.5">
                    <Label htmlFor="source-filter" className="text-xs">
                      Data Source
                    </Label>
                    <Select
                      value={filters.source || "all"}
                      onValueChange={(value) => {
                        const newFilters = { ...filters };
                        if (value === "all") {
                          delete newFilters.source;
                        } else {
                          newFilters.source = value;
                        }
                        setFilters(newFilters);
                        setPagination((prev) => ({ ...prev, page: 1 }));
                      }}
                    >
                      <SelectTrigger id="source-filter" className="h-8">
                        <SelectValue placeholder="All Sources" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Sources</SelectItem>
                        {filterOptions.sources.map((source) => (
                          <SelectItem key={source} value={source}>
                            {source === "PlacesAPI"
                              ? "Google Places API"
                              : source === "ERP"
                              ? "ERP System"
                              : source === "Manual"
                              ? "Manually Added"
                              : source}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex flex-col gap-1.5 mt-4">
                    <Label htmlFor="time-period-filter" className="text-xs">
                      Added Time Period
                    </Label>
                    <Select
                      value={filters.timePeriod || "all"}
                      onValueChange={(value) => {
                        const newFilters = { ...filters };
                        if (value === "all") {
                          delete newFilters.timePeriod;
                        } else {
                          newFilters.timePeriod = value;
                        }
                        setFilters(newFilters);
                        setPagination((prev) => ({ ...prev, page: 1 }));
                      }}
                    >
                      <SelectTrigger id="time-period-filter" className="h-8">
                        <SelectValue placeholder="All Time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Time</SelectItem>
                        <SelectItem value="7">Last 7 Days</SelectItem>
                        <SelectItem value="30">Last 30 Days</SelectItem>
                        <SelectItem value="90">Last 90 Days</SelectItem>
                        <SelectItem value="365">Last Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="flex items-center gap-1"
              >
                <X className="h-3.5 w-3.5" />
                Clear Filters
              </Button>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Main content */}
        {leads.length === 0 ? (
          <div className="bg-muted/30 p-8 rounded-lg flex flex-col items-center justify-center">
            <p className="text-lg mb-4">No leads found</p>
            <p className="text-sm text-muted-foreground mb-6">
              Import leads or sync with ERP to get started
            </p>
          </div>
        ) : filters.showDuplicatesOnly === "true" && leads.length === 0 ? (
          <div className="bg-muted/30 p-8 rounded-lg flex flex-col items-center justify-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg mb-4">No duplicate leads found</p>
            <p className="text-sm text-muted-foreground mb-6">
              All leads have unique Google Place IDs or no Place ID at all.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                const newFilters = { ...filters };
                delete newFilters.showDuplicatesOnly;
                setFilters(newFilters);
                setPagination((prev) => ({ ...prev, page: 1 }));
              }}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Show All Leads
            </Button>
          </div>
        ) : (
          <Tabs value={activeView} className="w-full">
            <TabsContent value="table" className="mt-0">
              <DataTable
                ref={dataTableRef}
                columns={createColumns(
                  toggleDuplicateExpansion,
                  expandedDuplicates
                )}
                data={processedLeads}
                pagination={pagination}
                onPaginationChange={(page, pageSize) => {
                  setPagination((prev) => ({ ...prev, page, pageSize }));
                }}
                onSearchChange={(search) => {
                  setSearchTerm(search);
                  setPagination((prev) => ({ ...prev, page: 1 })); // Reset to page 1 on search
                }}
                loading={loading}
              />
            </TabsContent>
            <TabsContent value="map" className="mt-0">
              <LeadsMap leads={allLeads.length > 0 ? allLeads : leads} />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
