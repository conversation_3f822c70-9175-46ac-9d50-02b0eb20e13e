"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Star, ChevronDown, ChevronRight, Info } from "lucide-react";
import { toast } from "sonner";
import LeadSyncSummary from "@/components/LeadSyncSummary";
import { logActivity, LogAction } from "@/lib/logger";
import { Loader } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import PrecomputedLeadGenerator from "@/components/PrecomputedLeadGenerator";
import PaginatedResultsTable from "@/components/PaginatedResultsTable";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface SearchParams {
  location: string;
  radius: string;
  type: string;
  keyword: string;
  maxResults: string;
}

interface ExtensiveSearchParams {
  location: string;
  radii: string[];
  type: string;
  keyword: string;
  maxResultsPerRadius: string;
}

interface PlaceResult {
  place_id: string;
  name: string;
  vicinity: string;
  formatted_address: string;
  business_status?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  types?: string[];
  maps_url?: string;
  canton?: string;
  primary_type?: string;
  street_name?: string;
  postal_code?: string;
  city?: string;
  country?: string;
}

const columns = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }: { row: { getValue: (key: string) => any } }) => (
      <div className="truncate max-w-[200px]" title={row.getValue("name")}>
        {row.getValue("name")}
      </div>
    ),
  },
  {
    accessorKey: "street_name",
    header: "Street",
    cell: ({ row }: { row: { getValue: (key: string) => any } }) => (
      <div
        className="truncate max-w-[200px]"
        title={row.getValue("street_name")}
      >
        {row.getValue("street_name")}
      </div>
    ),
  },
  {
    accessorKey: "postal_code",
    header: "PLZ",
  },
  {
    accessorKey: "city",
    header: "City",
  },
  {
    accessorKey: "canton",
    header: "Canton",
  },
  {
    accessorKey: "country",
    header: "Country",
  },
  {
    accessorKey: "business_status",
    header: "Status",
    cell: ({ row }: { row: { getValue: (key: string) => any } }) => (
      <div className="capitalize">
        {(row.getValue("business_status") as string)?.toLowerCase() ||
          "unknown"}
      </div>
    ),
  },
  {
    accessorKey: "primary_type",
    header: "Type",
  },
  {
    accessorKey: "place_id",
    header: "Place ID",
    cell: ({
      row,
    }: {
      row: { getValue: (key: string) => any; original?: Record<string, any> };
    }) => {
      const placeId = row.getValue("place_id");
      const googleName = row.original?.googleName;

      return (
        <div className="space-y-1">
          <div className="truncate max-w-[200px]" title={placeId}>
            {placeId || "Not fetched"}
          </div>
          {googleName && placeId === "N/A" && (
            <div className="text-xs text-red-500">No match: {googleName}</div>
          )}
          {googleName && placeId !== "N/A" && (
            <div className="text-xs text-green-500">Matched: {googleName}</div>
          )}
        </div>
      );
    },
  },
];

const getMatchSummary = (results: any[]) => {
  const total = results.length;
  const matches = results.filter((r) => r.place_id !== "N/A").length;
  const noMatches = total - matches;

  return {
    total,
    matches,
    noMatches,
    matchDetails: results.map((r) => ({
      erpName: r.name,
      googleName: r.googleName,
      matched: r.place_id !== "N/A",
    })),
  };
};

export default function LeadGeneratorPage() {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  // Lead sync summary state
  const [leadSyncSummary, setLeadSyncSummary] = useState({
    addedCount: 0,
    existingCount: 0,
    failedCount: 0,
    totalProcessed: 0,
    isLoading: false,
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({
    location: "",
    radius: "1000",
    type: "restaurant",
    keyword: "",
    maxResults: "20",
  });
  const [extensiveSearchParams, setExtensiveSearchParams] =
    useState<ExtensiveSearchParams>({
      location: "",
      radii: ["1000", "2000", "5000"],
      type: "restaurant",
      keyword: "",
      maxResultsPerRadius: "20",
    });
  const [results, setResults] = useState<PlaceResult[]>([]);
  const [selectedPlaces, setSelectedPlaces] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState("normal");
  const [precomputedLoading, setPrecomputedLoading] = useState(false);
  const [apiPreference, setApiPreference] = useState<"new" | "legacy">("new");

  // Function to visualize search results on the map
  const visualizeResultsOnMap = (results: any[]) => {
    if (typeof window === "undefined" || !window.visualizeSearchResultsOnMap) {
      console.log("visualizeSearchResultsOnMap function not available");
      return;
    }

    // Filter results with valid coordinates
    const mappedResults = results
      .filter(
        (place) =>
          place.location && place.location.latitude && place.location.longitude
      )
      .map((place) => ({
        lat: place.location.latitude,
        lng: place.location.longitude,
        place_id: place.place_id,
        name: place.name,
      }));

    console.log(
      `Visualizing ${mappedResults.length} results on the map out of ${results.length} total results`
    );

    if (mappedResults.length > 0) {
      window.visualizeSearchResultsOnMap(mappedResults);
    } else {
      console.log("No valid coordinates found in results");
    }
  };

  const handleSearch = async () => {
    setLoading(true);
    try {
      // Use fixed maximum results based on API preference
      const maxResults = apiPreference === "new" ? "20" : "60";

      // Ensure searchParams has the correct maxResults value
      if (searchParams.maxResults !== maxResults) {
        setSearchParams({
          ...searchParams,
          maxResults: maxResults,
        });
      }

      // Determine which API endpoint to use
      const endpoint =
        apiPreference === "legacy"
          ? "/api/places/legacy-search"
          : "/api/places/search";

      const params = new URLSearchParams({
        location: searchParams.location,
        radius: searchParams.radius,
        type: searchParams.type,
        maxResults: maxResults,
      });

      if (searchParams.keyword) {
        params.append("keyword", searchParams.keyword);
      }

      console.log(`Using ${apiPreference} API endpoint: ${endpoint}`);
      const response = await fetch(`${endpoint}?${params.toString()}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}):`, errorText);
        throw new Error(
          `Failed to fetch places: ${response.status} ${errorText.substring(
            0,
            100
          )}`
        );
      }

      const data = await response.json();
      setResults(data.results);

      // Visualize the results on the map
      visualizeResultsOnMap(data.results);

      if (data.results.length > 0) {
        toast.success(
          `Found ${data.results.length} places using ${apiPreference} API`
        );

        await logActivity(
          LogAction.LEAD_GENERATION,
          `Lead search completed (${apiPreference} API)`,
          {
            location: searchParams.location,
            radius: searchParams.radius,
            type: searchParams.type,
            api_used: apiPreference,
            results_count: data.results.length,
          }
        );
      } else {
        toast.warning(
          `No places found for your search criteria using ${apiPreference} API`
        );

        await logActivity(
          LogAction.LEAD_GENERATION,
          `Lead search returned no results (${apiPreference} API)`,
          {
            location: searchParams.location,
            radius: searchParams.radius,
            type: searchParams.type,
            api_used: apiPreference,
          }
        );
      }
    } catch (error) {
      console.error("Error searching for places:", error);
      toast.error(
        `Failed to search for places: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );

      await logActivity(
        LogAction.LEAD_GENERATION,
        `Lead search failed (${apiPreference} API)`,
        {
          location: searchParams.location,
          radius: searchParams.radius,
          type: searchParams.type,
          api_used: apiPreference,
          error: error instanceof Error ? error.message : "Unknown error",
        }
      );
    } finally {
      setLoading(false);
    }
  };

  const handlePrecomputedSearch = async (
    points: { lat: number; lng: number; radius: number }[]
  ) => {
    setPrecomputedLoading(true);

    // Array to store results count per point
    const resultsCountPerPoint: number[] = [];

    try {
      const data = await executePlacesSearch(
        points,
        extensiveSearchParams.type,
        extensiveSearchParams.keyword,
        extensiveSearchParams.maxResultsPerRadius,
        apiPreference
      );

      // Extract results counts from the API response if available
      if (data && data.resultsPerPoint && Array.isArray(data.resultsPerPoint)) {
        // Use the counts directly from the API
        resultsCountPerPoint.push(...data.resultsPerPoint);
      }

      // Return the search results to the PrecomputedLeadGenerator component
      // Map the results to the format expected by the map visualization
      const mappedResults = results
        .map((place) => {
          // Log the location data for debugging
          console.log(`Mapping place ${place.name} location:`, place.location);

          // Handle both API formats
          return {
            lat: place.location?.latitude || 0,
            lng: place.location?.longitude || 0,
            place_id: place.place_id,
            name: place.name,
          };
        })
        .filter((point) => point.lat !== 0 && point.lng !== 0);

      console.log(
        `Mapped ${mappedResults.length} points with valid coordinates out of ${results.length} total results`
      );

      // If we didn't get results counts from the API, use a default value
      if (resultsCountPerPoint.length === 0 && points.length > 0) {
        // Estimate results per point (this is just an approximation)
        const avgResultsPerPoint = Math.ceil(results.length / points.length);
        for (let i = 0; i < points.length; i++) {
          resultsCountPerPoint.push(avgResultsPerPoint);
        }
        console.log(
          "Using estimated results count per point:",
          resultsCountPerPoint
        );
      }

      return {
        results: mappedResults,
        resultsCountPerPoint: resultsCountPerPoint,
      };
    } catch (error) {
      return {
        results: [],
        resultsCountPerPoint: [],
      }; // Return empty arrays on error
    } finally {
      setPrecomputedLoading(false);
    }
  };

  const executePlacesSearch = async (
    points: { lat: number; lng: number; radius: number }[],
    type: string,
    keyword: string,
    maxResultsPerPointStr: string,
    apiPref: "new" | "legacy"
  ) => {
    const searchType = apiPref === "legacy" ? "Legacy (Paginated)" : "New";
    const endpoint =
      apiPref === "legacy"
        ? "/api/places/legacy-extensive-search"
        : "/api/places/extensive-search";

    // Generate a search ID for this search session
    const searchId = `search_${Date.now()}`;

    console.log(`Executing ${searchType} search with ${points.length} points:`);
    console.log(`Endpoint: ${endpoint}`);
    console.log(`Search ID: ${searchId}`);
    console.log("Search parameters:", { type, keyword, maxResultsPerPointStr });

    try {
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Search-ID": searchId, // Pass the search ID in the header
        },
        body: JSON.stringify({
          points,
          type: type,
          keyword: keyword || undefined,
          maxResultsPerPoint: parseInt(maxResultsPerPointStr) || 20,
          includeResultsPerPoint: true, // Request per-point result counts
          searchId: searchId, // Also include in the body for redundancy
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `${searchType} search API error:`,
          response.status,
          errorText
        );
        toast.error(
          `${searchType} search failed: ${errorText.substring(0, 100)}`
        );
        throw new Error(
          `Failed to perform ${searchType.toLowerCase()} places search: ${
            response.status
          } ${errorText}`
        );
      }

      const data = await response.json();
      console.log(`${searchType} search results:`, {
        total: data.total,
        failuresCount: data.failures?.length || 0,
      });

      // Track results per point if available
      if (data.resultsPerPoint) {
        console.log("Results per point:", data.resultsPerPoint);

        // Find the maximum results for any point
        if (data.resultsPerPoint.length > 0) {
          const maxResults = Math.max(...data.resultsPerPoint);
          const avgResults =
            data.resultsPerPoint.reduce(
              (sum: number, count: number) => sum + count,
              0
            ) / data.resultsPerPoint.length;

          console.log("=== RESULTS PER POINT STATISTICS ===");
          console.log(`Maximum results for any point: ${maxResults}`);
          console.log(`Average results per point: ${avgResults.toFixed(2)}`);
        }
      }

      if (data.results && data.results.length > 0) {
        console.log("Sample result:", data.results[0]);
      }

      // Set the results in state
      setResults(data.results);
      setSelectedPlaces(new Set());

      // Visualize the results on the map
      visualizeResultsOnMap(data.results);

      toast.success(
        `Found ${data.total || 0} unique places using ${searchType} API`
      );

      await logActivity(
        LogAction.LEAD_GENERATION,
        `Points-based lead search completed (${searchType})`,
        {
          search_points_count: points.length,
          total_results: data.total || 0,
          type: type,
          api_used: apiPref,
        }
      );

      // Return the data for further processing
      return data;
    } catch (error) {
      console.error(
        `Error in ${searchType.toLowerCase()} places search execution:`,
        error
      );
      if (
        !(
          error instanceof Error &&
          error.message.startsWith(
            `Failed to perform ${searchType.toLowerCase()} places search`
          )
        )
      ) {
        toast.error(
          `Failed to perform ${searchType.toLowerCase()} places search`
        );
      }

      await logActivity(
        LogAction.LEAD_GENERATION,
        `Points-based lead search failed (${searchType})`,
        {
          error: error instanceof Error ? error.message : "Unknown error",
          api_used: apiPref,
        }
      );
      throw error;
    }
  };

  const handleSelectOne = (checked: boolean, id: string) => {
    const newSelected = new Set(selectedPlaces);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedPlaces(newSelected);
  };

  const handleSaveLeads = async () => {
    setSaving(true);
    // Reset lead sync summary and set loading state
    setLeadSyncSummary({
      addedCount: 0,
      existingCount: 0,
      failedCount: 0,
      totalProcessed: 0,
      isLoading: true,
    });

    try {
      console.log("Selected places count:", selectedPlaces.size);
      console.log("Selected place IDs:", Array.from(selectedPlaces));

      const selectedLeads = results.filter((place) => {
        const placeId = place.place_id;
        const isSelected = selectedPlaces.has(placeId);
        console.log(`Checking place ${place.name}:`, {
          placeId,
          isSelected,
          hasLocation: !!place.location,
          locationData: place.location,
        });
        return isSelected;
      });

      console.log("Filtered leads count:", selectedLeads.length);
      if (selectedLeads.length === 0) {
        console.log("No leads found matching selected IDs", {
          selectedIds: Array.from(selectedPlaces),
          availableIds: results.map((p) => p.place_id),
        });
      } else {
        console.log(
          "First selected lead details:",
          JSON.stringify(selectedLeads[0], null, 2)
        );
      }

      // Split leads into batches to avoid payload size limits
      const LEADS_BATCH_SIZE = 100; // Process 100 leads per batch
      const leadBatches = [];
      for (let i = 0; i < selectedLeads.length; i += LEADS_BATCH_SIZE) {
        leadBatches.push(selectedLeads.slice(i, i + LEADS_BATCH_SIZE));
      }

      console.log(
        `Saving ${selectedLeads.length} leads in ${leadBatches.length} batches`
      );

      let totalAdded = 0;
      let totalExisting = 0;
      let totalFailed = 0;

      // Process each batch sequentially
      for (let batchIndex = 0; batchIndex < leadBatches.length; batchIndex++) {
        const batch = leadBatches[batchIndex];

        console.log(
          `Processing leads batch ${batchIndex + 1}/${
            leadBatches.length
          } with ${batch.length} leads`
        );

        try {
          const response = await fetch("/api/leads/add", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              leads: batch,
              batchInfo: {
                batchIndex: batchIndex + 1,
                totalBatches: leadBatches.length,
                batchSize: batch.length,
                totalLeads: selectedLeads.length,
              },
            }),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `Error saving leads batch ${batchIndex + 1}:`,
              errorText
            );
            throw new Error(
              `Failed to save leads batch ${batchIndex + 1}: ${errorText}`
            );
          }

          const data = await response.json();
          totalAdded += data.added || 0;
          totalExisting += batch.length - (data.added || 0);

          console.log(
            `Leads batch ${batchIndex + 1} completed: ${data.added} added`
          );
        } catch (batchError) {
          console.error(
            `Error processing leads batch ${batchIndex + 1}:`,
            batchError
          );
          totalFailed += batch.length;

          // Continue with next batch instead of failing completely
          console.log(
            `Continuing with next batch after error in batch ${batchIndex + 1}`
          );
        }
      }

      // Update lead sync summary with the cumulative results
      setLeadSyncSummary({
        addedCount: totalAdded,
        existingCount: totalExisting,
        failedCount: totalFailed,
        totalProcessed: selectedLeads.length,
        isLoading: false,
      });

      if (totalAdded > 0) {
        toast.success(
          `Added ${totalAdded} leads${
            totalFailed > 0 ? ` (${totalFailed} failed)` : ""
          }`
        );
      } else if (totalFailed > 0) {
        toast.error(`Failed to add ${totalFailed} leads`);
      } else {
        toast.info("No new leads were added");
      }

      setSelectedPlaces(new Set());

      await logActivity(
        LogAction.LEAD_SAVE,
        "Leads added to database in batches",
        {
          lead_count: totalAdded,
          existing_count: totalExisting,
          failed_count: totalFailed,
          total_processed: selectedLeads.length,
          total_batches: leadBatches.length,
          batch_size: LEADS_BATCH_SIZE,
          lead_types: [
            ...new Set(
              selectedLeads.map((lead) => lead.primary_type).filter(Boolean)
            ),
          ],
        }
      );
    } catch (error) {
      console.error("Error saving leads:", error);
      const errorMsg =
        error instanceof Error ? error.message : "An unknown error occurred";
      toast.error(`Failed to save leads: ${errorMsg.substring(0, 100)}`);

      // Update lead sync summary with error information
      setLeadSyncSummary({
        addedCount: 0,
        existingCount: 0,
        failedCount: selectedPlaces.size,
        totalProcessed: selectedPlaces.size,
        isLoading: false,
      });

      await logActivity(LogAction.LEAD_SAVE, "Failed to add leads", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allPlaceIds = new Set(results.map((place) => place.place_id));
      setSelectedPlaces(allPlaceIds);
    } else {
      setSelectedPlaces(new Set());
    }
  };

  return (
    <div className="container mx-auto py-4 px-4">
      <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
        <Card className="mb-6 md:col-span-1">
          <CardHeader>
            <CardTitle>Lead Generator</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-6 p-4 border rounded-md bg-yellow-50/50">
              <Label className="font-semibold mb-2 block">API Preference</Label>
              <RadioGroup
                defaultValue="new"
                value={apiPreference}
                onValueChange={(value: "new" | "legacy") =>
                  setApiPreference(value)
                }
                className="flex space-x-4 mb-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="new" id="api-new" />
                  <Label htmlFor="api-new">New API (V1)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="legacy" id="api-legacy" />
                  <Label htmlFor="api-legacy">Legacy API</Label>
                </div>
              </RadioGroup>
              <div className="flex items-center text-xs text-muted-foreground bg-yellow-100 p-2 rounded">
                <Info className="h-4 w-4 mr-1.5 flex-shrink-0 text-yellow-600" />
                <span>
                  Use Legacy API to fetch up to 60 results per search point
                  (pagination). New API is limited to 20.
                </span>
              </div>
            </div>

            <Tabs
              defaultValue="normal"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="normal">Location Search</TabsTrigger>
                <TabsTrigger value="precomputed" id="precomputed-tab">
                  Precomputed
                </TabsTrigger>
              </TabsList>

              <TabsContent value="normal" className="space-y-4">
                <div className="bg-blue-50/50 rounded-md p-4">
                  <div className="grid gap-6">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label
                          htmlFor="location"
                          className="text-base font-medium"
                        >
                          Location
                        </Label>
                        <div className="text-xs text-muted-foreground">
                          Select a city or enter a custom location
                        </div>
                      </div>

                      {/* Preselected Cities */}
                      <div className="grid grid-cols-4 gap-2 mb-2">
                        {[
                          { name: "Zürich", coords: "47.3769,8.5417" },
                          { name: "Bern", coords: "46.9480,7.4474" },
                          { name: "Geneva", coords: "46.2044,6.1432" },
                          { name: "Basel", coords: "47.5596,7.5886" },
                          { name: "Lausanne", coords: "46.5197,6.6323" },
                          { name: "Lucerne", coords: "47.0502,8.3093" },
                          { name: "St. Gallen", coords: "47.4245,9.3767" },
                          { name: "Lugano", coords: "46.0037,8.9511" },
                        ].map((city) => (
                          <Button
                            key={city.name}
                            variant={
                              searchParams.location === city.coords
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            onClick={() =>
                              setSearchParams({
                                ...searchParams,
                                location: city.coords,
                              })
                            }
                            className="h-8"
                          >
                            {city.name}
                          </Button>
                        ))}
                      </div>

                      <div className="flex gap-2">
                        <Input
                          id="location"
                          placeholder="e.g. Zurich, Switzerland or 47.3769,8.5417"
                          value={searchParams.location}
                          onChange={(e) =>
                            setSearchParams({
                              ...searchParams,
                              location: e.target.value,
                            })
                          }
                          className="flex-1"
                        />
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Enter a location name or coordinates
                        (latitude,longitude)
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="type" className="text-base font-medium">
                          Place Type
                        </Label>
                        <Select
                          value={searchParams.type}
                          onValueChange={(value) =>
                            setSearchParams({ ...searchParams, type: value })
                          }
                        >
                          <SelectTrigger id="type">
                            <SelectValue placeholder="Select a type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="restaurant">
                              Restaurant
                            </SelectItem>
                            <SelectItem value="cafe">Cafe</SelectItem>
                            <SelectItem value="bar">Bar</SelectItem>
                            <SelectItem value="bakery">Bakery</SelectItem>
                            <SelectItem value="supermarket">
                              Supermarket
                            </SelectItem>
                            <SelectItem value="store">Store</SelectItem>
                            <SelectItem value="clothing_store">
                              Clothing Store
                            </SelectItem>
                            <SelectItem value="shoe_store">
                              Shoe Store
                            </SelectItem>
                            <SelectItem value="jewelry_store">
                              Jewelry Store
                            </SelectItem>
                            <SelectItem value="department_store">
                              Department Store
                            </SelectItem>
                            <SelectItem value="shopping_mall">
                              Shopping Mall
                            </SelectItem>
                            <SelectItem value="hotel">Hotel</SelectItem>
                            <SelectItem value="lodging">Lodging</SelectItem>
                            <SelectItem value="bank">Bank</SelectItem>
                            <SelectItem value="atm">ATM</SelectItem>
                            <SelectItem value="pharmacy">Pharmacy</SelectItem>
                            <SelectItem value="doctor">Doctor</SelectItem>
                            <SelectItem value="hospital">Hospital</SelectItem>
                            <SelectItem value="gym">Gym</SelectItem>
                            <SelectItem value="spa">Spa</SelectItem>
                          </SelectContent>
                        </Select>
                        <div className="text-xs text-muted-foreground">
                          Select the type of place you want to search for
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="keyword"
                          className="text-base font-medium"
                        >
                          Keyword (optional)
                        </Label>
                        <Input
                          id="keyword"
                          placeholder="e.g. pizza, coffee, organic, etc."
                          value={searchParams.keyword}
                          onChange={(e) =>
                            setSearchParams({
                              ...searchParams,
                              keyword: e.target.value,
                            })
                          }
                        />
                        <div className="text-xs text-muted-foreground">
                          Add a keyword to refine your search results
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label
                          htmlFor="radius"
                          className="text-base font-medium"
                        >
                          Search Radius (meters)
                        </Label>
                        <div className="flex gap-2">
                          {[500, 1000, 2000, 5000].map((r) => (
                            <Button
                              key={r}
                              variant={
                                searchParams.radius === r.toString()
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                setSearchParams({
                                  ...searchParams,
                                  radius: r.toString(),
                                })
                              }
                              className="flex-1 h-8"
                            >
                              {r}m
                            </Button>
                          ))}
                        </div>
                        <Input
                          id="radius"
                          type="number"
                          placeholder="1000"
                          value={searchParams.radius}
                          onChange={(e) =>
                            setSearchParams({
                              ...searchParams,
                              radius: e.target.value,
                            })
                          }
                        />
                        <div className="text-xs text-muted-foreground">
                          Recommended: 500-5000m. Maximum allowed by Google:
                          50,000m
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="maxResults"
                          className="text-base font-medium"
                        >
                          API Selection & Maximum Results
                        </Label>
                        <div className="flex gap-2">
                          <Button
                            variant={
                              apiPreference === "new" ? "default" : "outline"
                            }
                            size="sm"
                            onClick={() => {
                              setApiPreference("new");
                              setSearchParams({
                                ...searchParams,
                                maxResults: "20",
                              });
                            }}
                            className="flex-1 h-8"
                          >
                            New API (20 results)
                          </Button>
                          <Button
                            variant={
                              apiPreference === "legacy" ? "default" : "outline"
                            }
                            size="sm"
                            onClick={() => {
                              setApiPreference("legacy");
                              setSearchParams({
                                ...searchParams,
                                maxResults: "60",
                              });
                            }}
                            className="flex-1 h-8"
                          >
                            Legacy API (60 results)
                          </Button>
                        </div>
                        <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-md">
                          <Info className="h-4 w-4 text-blue-500" />
                          <div className="text-xs text-muted-foreground">
                            {apiPreference === "new"
                              ? "Using New API with parallel processing (6 concurrent requests), 600 requests/minute rate limiting, and 20 results maximum"
                              : "Using Legacy API with parallel processing (6 concurrent requests), 600 requests/minute rate limiting, and 60 results maximum (uses pagination)"}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        onClick={handleSearch}
                        disabled={loading || !searchParams.location}
                        size="lg"
                        className="px-8"
                      >
                        {loading ? (
                          <>
                            <Loader className="mr-2 h-5 w-5 animate-spin" />
                            Searching...
                          </>
                        ) : (
                          "Search for Leads"
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="precomputed" className="space-y-4">
                <PrecomputedLeadGenerator
                  onStartSearch={handlePrecomputedSearch}
                  loading={precomputedLoading}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {results.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Places API Results</CardTitle>
            <div className="flex items-center gap-2">
              <div className="text-sm text-muted-foreground">
                {`${selectedPlaces.size} of ${results.length} places selected`}
              </div>
              <Button
                onClick={handleSaveLeads}
                disabled={saving || selectedPlaces.size === 0}
                size="sm"
              >
                {saving ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Add Selected Leads"
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Selection Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="select-all"
                    checked={
                      selectedPlaces.size > 0 &&
                      selectedPlaces.size === results.length
                    }
                    onCheckedChange={(checked) =>
                      handleSelectAll(checked as boolean)
                    }
                  />
                  <Label htmlFor="select-all" className="text-sm font-medium">
                    Select All Results
                  </Label>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Export results to CSV
                    const csvContent =
                      "data:text/csv;charset=utf-8," +
                      "Name,Street,Postal Code,City,Canton,Country,Business Status,Type,Place ID\n" +
                      results
                        .map(
                          (r) =>
                            `"${r.name || ""}","${r.street_name || ""}","${
                              r.postal_code || ""
                            }","${r.city || ""}","${r.canton || ""}","${
                              r.country || ""
                            }","${r.business_status || ""}","${
                              r.primary_type || ""
                            }","${r.place_id || ""}"`
                        )
                        .join("\n");

                    const encodedUri = encodeURI(csvContent);
                    const link = document.createElement("a");
                    link.setAttribute("href", encodedUri);
                    link.setAttribute(
                      "download",
                      `places_results_${new Date()
                        .toISOString()
                        .slice(0, 10)}.csv`
                    );
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    toast.success("Results exported to CSV");
                  }}
                >
                  Export to CSV
                </Button>
              </div>

              {/* Paginated Results Table */}
              <div className="rounded-md border">
                <PaginatedResultsTable
                  results={results.map((place) => ({
                    lat: place.location?.latitude || 0,
                    lng: place.location?.longitude || 0,
                    place_id: place.place_id,
                    name: place.name,
                  }))}
                  itemsPerPage={100}
                  onResultClick={(result) => {
                    // Center the map on the selected result
                    if (
                      typeof window !== "undefined" &&
                      window.visualizeSearchResultsOnMap
                    ) {
                      // Highlight this specific result
                      window.visualizeSearchResultsOnMap([result]);

                      // Scroll to the precomputed tab which contains the map
                      const precomputedTab =
                        document.getElementById("precomputed-tab");
                      if (precomputedTab) {
                        precomputedTab.scrollIntoView({ behavior: "smooth" });
                        // Activate the precomputed tab if it's not already active
                        setActiveTab("precomputed");
                      }

                      toast.info(
                        `Selected: ${result.name || "Unnamed location"}`
                      );
                    }
                  }}
                />
              </div>

              {/* Legacy Table for Selection */}
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" size="sm" className="w-full">
                    <ChevronDown className="h-4 w-4 mr-2" />
                    Show Detailed Selection Table
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-4">
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">
                            <Checkbox
                              checked={
                                selectedPlaces.size > 0 &&
                                selectedPlaces.size === results.length
                              }
                              onCheckedChange={(checked) =>
                                handleSelectAll(checked as boolean)
                              }
                              aria-label="Select all"
                            />
                          </TableHead>
                          {columns.map((column) => (
                            <TableHead key={column.accessorKey}>
                              {column.header}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {results.map((place) => (
                          <TableRow
                            key={place.place_id || `place_${Math.random()}`}
                          >
                            <TableCell>
                              <Checkbox
                                checked={selectedPlaces.has(
                                  place.place_id || ""
                                )}
                                onCheckedChange={(checked) => {
                                  handleSelectOne(
                                    checked as boolean,
                                    place.place_id || ""
                                  );
                                }}
                                aria-label={`Select ${place.name}`}
                              />
                            </TableCell>
                            {columns.map((column) => (
                              <TableCell key={column.accessorKey}>
                                {column.cell ? (
                                  column.cell({
                                    row: {
                                      getValue: (key: string) =>
                                        (place as Record<string, any>)[key],
                                      original: place,
                                    },
                                  })
                                ) : (
                                  <div
                                    className="truncate max-w-[200px]"
                                    title={String(
                                      (place as Record<string, any>)[
                                        column.accessorKey
                                      ] ?? ""
                                    )}
                                  >
                                    {(place as Record<string, any>)[
                                      column.accessorKey
                                    ] ?? ""}
                                  </div>
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>

            {/* Lead Sync Summary */}
            {(leadSyncSummary.isLoading ||
              leadSyncSummary.totalProcessed > 0) && (
              <div className="mt-4">
                <LeadSyncSummary
                  addedCount={leadSyncSummary.addedCount}
                  existingCount={leadSyncSummary.existingCount}
                  failedCount={leadSyncSummary.failedCount}
                  totalProcessed={leadSyncSummary.totalProcessed}
                  isLoading={leadSyncSummary.isLoading}
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
