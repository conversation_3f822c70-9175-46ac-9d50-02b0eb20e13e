"use client";

// Reminder: Install papaparse and its types: npm install papaparse @types/papaparse
// or: yarn add papaparse @types/papaparse

import React, { useState, useCallback, useRef, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader, FileIcon, RefreshCw, MapPin, Table } from "lucide-react";
import { toast } from "sonner";
import Papa, { ParseR<PERSON>ult, ParseError } from "papaparse"; // Using papaparse for robust CSV parsing
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ApiProgressDisplay from "@/components/ApiProgressDisplay";

// Restore the original namespace declaration
declare namespace google.maps {
  // Basic types needed - expand if necessary
  class Map {
    constructor(mapDiv: Element | null, opts?: MapOptions);
    setCenter(latLng: LatLng | LatLngLiteral): void;
    setZoom(zoom: number): void;
    getCenter(): LatLng;
    getZoom(): number;
    set(key: string, value: any): void;
    get(key: string): any;
    fitBounds(
      bounds: LatLngBounds | LatLngBoundsLiteral,
      padding?: number | Padding
    ): void;
  }
  interface MapOptions {
    center?: LatLng | LatLngLiteral;
    zoom?: number;
    mapTypeId?: string | MapTypeId;
    mapTypeControl?: boolean;
    streetViewControl?: boolean;
    fullscreenControl?: boolean;
  }
  class LatLng {
    constructor(lat: number, lng: number);
    lat(): number;
    lng(): number;
  }
  interface LatLngLiteral {
    lat: number;
    lng: number;
  }
  enum MapTypeId {
    ROADMAP,
  }
  class Circle {
    constructor(opts?: CircleOptions);
    setMap(map: Map | null): void;
    getCenter(): LatLng;
    getRadius(): number;
    getBounds(): LatLngBounds;
  }
  interface CircleOptions {
    map?: Map;
    center?: LatLng | LatLngLiteral;
    radius?: number;
    fillColor?: string;
    fillOpacity?: number;
    strokeColor?: string;
    strokeWeight?: number;
  }
  class Marker {
    constructor(opts?: MarkerOptions);
    setMap(map: Map | null): void;
    setPosition(latLng: LatLng | LatLngLiteral): void;
    setTitle(title: string): void;
    setIcon(icon: string | Icon | Symbol): void;
    getPosition(): LatLng | null;
  }
  interface MarkerOptions {
    position: LatLng | LatLngLiteral;
    map?: Map;
    title?: string;
    icon?: string | Icon | Symbol;
    label?: string | MarkerLabel;
  }
  interface Icon {
    url: string;
    size?: Size;
    scaledSize?: Size;
    origin?: Point;
    anchor?: Point;
  }
  class Size {
    constructor(width: number, height: number);
  }
  class Point {
    constructor(x: number, y: number);
  }
  interface MarkerLabel {
    text: string;
    color?: string;
    fontWeight?: string;
    fontSize?: string;
  }
  interface Symbol {
    path: SymbolPath | string;
    fillColor?: string;
    fillOpacity?: number;
    scale?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
  }
  enum SymbolPath {
    CIRCLE,
    FORWARD_CLOSED_ARROW,
    FORWARD_OPEN_ARROW,
    BACKWARD_CLOSED_ARROW,
    BACKWARD_OPEN_ARROW,
  }
  class InfoWindow {
    constructor(opts?: InfoWindowOptions);
    setPosition(position: LatLng | LatLngLiteral): void;
    open(mapOrOptions?: Map | InfoWindowOpenOptions): void;
    close(): void;
    setContent(content: string | Node): void;
  }
  interface InfoWindowOptions {
    content?: string | Node;
    position?: LatLng | LatLngLiteral;
  }
  interface InfoWindowOpenOptions {
    map?: Map;
    anchor?: Marker;
  }
  class LatLngBounds {
    constructor(sw?: LatLng | LatLngLiteral, ne?: LatLng | LatLngLiteral);
    extend(point: LatLng | LatLngLiteral): LatLngBounds;
    getCenter(): LatLng;
    getNorthEast(): LatLng;
    getSouthWest(): LatLng;
    contains(latLng: LatLng | LatLngLiteral): boolean;
  }
  interface LatLngBoundsLiteral {
    east: number;
    north: number;
    south: number;
    west: number;
  }
  interface Padding {
    top: number;
    right: number;
    bottom: number;
    left: number;
  }

  // Polygon related types
  class Polygon {
    constructor(opts?: PolygonOptions);
    setMap(map: Map | null): void;
    getPath(): MVCArray<LatLng>;
    getPaths(): MVCArray<MVCArray<LatLng>>;
  }

  interface PolygonOptions {
    paths?:
      | MVCArray<LatLng>
      | LatLng[]
      | LatLngLiteral[]
      | MVCArray<MVCArray<LatLng>>
      | MVCArray<LatLng>[];
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
    fillColor?: string;
    fillOpacity?: number;
    editable?: boolean;
    draggable?: boolean;
    map?: Map;
  }

  class MVCArray<T> {
    constructor(array?: T[]);
    forEach(callback: (elem: T, i: number) => void): void;
    getAt(i: number): T;
    getLength(): number;
    push(elem: T): number;
  }

  // Control Position enum
  enum ControlPosition {
    TOP_CENTER,
    TOP_LEFT,
    TOP_RIGHT,
    BOTTOM_CENTER,
    BOTTOM_LEFT,
    BOTTOM_RIGHT,
    LEFT_CENTER,
    RIGHT_CENTER,
  }

  // Drawing Manager
  namespace drawing {
    class DrawingManager {
      constructor(options?: DrawingManagerOptions);
      setMap(map: Map | null): void;
      setDrawingMode(drawingMode: OverlayType | null): void;
      getDrawingMode(): OverlayType | null;
    }

    interface DrawingManagerOptions {
      drawingMode?: OverlayType | null;
      drawingControl?: boolean;
      drawingControlOptions?: DrawingControlOptions;
      polygonOptions?: PolygonOptions;
      map?: Map;
    }

    interface DrawingControlOptions {
      position?: ControlPosition;
      drawingModes?: OverlayType[];
    }

    enum OverlayType {
      POLYGON,
      POLYLINE,
      RECTANGLE,
      CIRCLE,
      MARKER,
    }
  }

  // Geometry library
  namespace geometry {
    namespace poly {
      function containsLocation(point: LatLng, polygon: Polygon): boolean;
    }
  }

  namespace event {
    function addListener(
      instance: object,
      eventName: string,
      handler: (...args: any[]) => void
    ): MapsEventListener;
    function clearInstanceListeners(instance: object): void;
    function trigger(instance: object, eventName: string, ...args: any[]): void;
  }
  interface MapsEventListener {
    remove(): void;
  }
}

interface PrecomputedPoint {
  lat: number;
  lng: number;
  radius: number;
}

interface PreloadedCsvFile {
  name: string;
  displayName: string;
  path: string;
}

interface PrecomputedLeadGeneratorProps {
  onStartSearch: (points: PrecomputedPoint[]) => Promise<{
    results: SearchResultPoint[];
    resultsCountPerPoint?: number[]; // Optional array of result counts per point
  }>;
  loading: boolean;
}

// Interface for saved search state
interface SavedSearchState {
  timestamp: number;
  points: PrecomputedPoint[];
  partialResults: SearchResultPoint[];
  resultsCountPerPoint: number[];
  searchProgress: number; // Percentage of completion
  searchType: string;
  searchKeyword?: string;
}

// Interface for search results
interface SearchResultPoint {
  lat: number;
  lng: number;
  place_id?: string; // Optional place ID
  name?: string; // Optional place name
}

const PrecomputedLeadGenerator: React.FC<PrecomputedLeadGeneratorProps> = ({
  onStartSearch,
  loading,
}) => {
  // File state is used in the file upload handler
  const [, setFile] = useState<File | null>(null);
  const [parsedPoints, setParsedPoints] = useState<PrecomputedPoint[]>([]);
  const [parseError, setParseError] = useState<string | null>(null);
  const [isParsing, setIsParsing] = useState(false);
  const [preloadedFiles, setPreloadedFiles] = useState<PreloadedCsvFile[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>("preloaded");
  const [isOptimizingArea, setIsOptimizingArea] = useState(false);

  // Statistics tracking
  const [maxResultsCount, setMaxResultsCount] = useState<number>(0);
  const [resultsCountPerPoint, setResultsCountPerPoint] = useState<number[]>(
    []
  );
  const [averageResultsPerPoint, setAverageResultsPerPoint] =
    useState<number>(0);

  // Search recovery state
  const [savedSearchState, setSavedSearchState] =
    useState<SavedSearchState | null>(null);
  const [isRecoveryAvailable, setIsRecoveryAvailable] =
    useState<boolean>(false);
  const [searchProgress, setSearchProgress] = useState<number>(0);
  const [isSavingResults, setIsSavingResults] = useState<boolean>(false);

  // API progress tracking
  const [apiCallsTotal, setApiCallsTotal] = useState<number>(0);
  const [apiCallsCompleted, setApiCallsCompleted] = useState<number>(0);
  const [apiCallsFailed, setApiCallsFailed] = useState<number>(0);
  const [apiCallsInProgress, setApiCallsInProgress] = useState<number>(0);
  const [showDebugPanel, setShowDebugPanel] = useState<boolean>(false);

  // Map related state and refs
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const drawingManagerRef = useRef<google.maps.drawing.DrawingManager | null>(
    null
  );
  const [mapLoaded, setMapLoaded] = useState(false);
  const currentCirclesRef = useRef<google.maps.Circle[]>([]); // Ref to keep track of circles
  const currentMarkersRef = useRef<google.maps.Marker[]>([]); // Ref to keep track of result markers
  const [searchResults, setSearchResults] = useState<SearchResultPoint[]>([]); // State for search results
  const [selectedArea, setSelectedArea] = useState<google.maps.Polygon | null>(
    null
  );

  // Function to clear result markers from the map
  const clearResultMarkers = () => {
    console.log(`Clearing ${currentMarkersRef.current.length} markers...`);
    currentMarkersRef.current.forEach((marker) => {
      google.maps.event?.clearInstanceListeners(marker);
      marker.setMap(null);
    });
    currentMarkersRef.current = []; // Reset the ref array
  };

  // Function to clear circles from the map
  const clearMapVisualization = () => {
    console.log(`Clearing ${currentCirclesRef.current.length} circles...`);
    currentCirclesRef.current.forEach((circle) => {
      // Use google.maps.event
      google.maps.event?.clearInstanceListeners(circle);
      circle.setMap(null);
    });
    currentCirclesRef.current = []; // Reset the ref array

    // Also clear any result markers
    clearResultMarkers();
  };

  // Adjust map view function (made generic to handle any points with lat/lng)
  const adjustMapView = useCallback(
    (points: { lat: number; lng: number }[]) => {
      if (!googleMapRef.current || points.length === 0) return;

      const map = googleMapRef.current;
      if (points.length === 1) {
        // Center on the single point with a reasonable zoom
        map.setCenter({ lat: points[0].lat, lng: points[0].lng });
        map.setZoom(13); // Adjust zoom level as needed
      } else {
        // Create bounds and fit the map
        const bounds = new google.maps.LatLngBounds();
        points.forEach((p) => bounds.extend({ lat: p.lat, lng: p.lng }));
        // Remove type assertion - rely on namespace definition
        map.fitBounds(bounds);
      }
    },
    [googleMapRef]
  );

  // Function to visualize search result points as markers
  const visualizeSearchResultPoints = useCallback(
    (points: SearchResultPoint[]) => {
      if (!googleMapRef.current) return;

      // Clear previous markers first
      clearResultMarkers();

      // Log the points for debugging
      console.log(`Visualizing ${points.length} search result points:`, points);

      const markers: google.maps.Marker[] = [];
      points.forEach((point) => {
        if (!googleMapRef.current) return;

        // Skip points with invalid coordinates
        if (!point.lat || !point.lng) {
          console.log(`Skipping point with invalid coordinates:`, point);
          return;
        }

        // Create a marker for each point
        const marker = new google.maps.Marker({
          map: googleMapRef.current,
          position: { lat: point.lat, lng: point.lng },
          title:
            point.name || `${point.lat.toFixed(6)}, ${point.lng.toFixed(6)}`,
          // Use a different color for result markers
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: "#FF0000", // Red color
            fillOpacity: 1,
            strokeWeight: 1,
            strokeColor: "#FFFFFF",
            scale: 8, // Size of the marker
          },
        });

        // Add tooltip/infowindow with more information
        const infoWindow = new google.maps.InfoWindow({
          content: `<div style="padding: 8px; max-width: 200px;">
                <strong>${point.name || "Location"}</strong><br>
                <strong>Coordinates:</strong> ${point.lat.toFixed(
                  6
                )}, ${point.lng.toFixed(6)}<br>
                ${
                  point.place_id
                    ? `<strong>Place ID:</strong> ${point.place_id}`
                    : ""
                }
               </div>`,
        });

        google.maps.event.addListener(marker, "click", () => {
          if (googleMapRef.current) {
            infoWindow.open({
              map: googleMapRef.current,
              anchor: marker,
            });
          }
        });

        markers.push(marker);
      });

      // Store new markers
      currentMarkersRef.current = markers;
      console.log(`Visualized ${markers.length} search result points.`);
    },
    [googleMapRef]
  );

  // Move visualization function here, before the useEffect that uses it
  const visualizePrecomputedPoints = useCallback(
    (points: PrecomputedPoint[]) => {
      if (!googleMapRef.current) return;
      // Clear previous circles first
      clearMapVisualization();

      const circles: google.maps.Circle[] = [];
      points.forEach((point) => {
        if (!googleMapRef.current) return;

        const circle = new google.maps.Circle({
          map: googleMapRef.current,
          center: { lat: point.lat, lng: point.lng },
          radius: point.radius,
          fillColor: "#1d4ed8", // Consistent blue color
          fillOpacity: 0.3,
          strokeColor: "#1e3a8a",
          strokeWeight: 1,
        });

        // Add tooltip/infowindow
        const infoWindow = new google.maps.InfoWindow({
          content: `<div style="padding: 5px;">
                  <strong>Radius:</strong> ${Math.round(point.radius)}m<br/>
                  <strong>Coords:</strong> ${point.lat.toFixed(
                    4
                  )}, ${point.lng.toFixed(4)}
                 </div>`,
        });

        google.maps.event.addListener(circle, "mouseover", () => {
          infoWindow.setPosition({ lat: point.lat, lng: point.lng });
          if (googleMapRef.current) {
            infoWindow.open({
              map: googleMapRef.current,
            });
          }
        });

        google.maps.event.addListener(circle, "mouseout", () => {
          infoWindow.close();
        });

        circles.push(circle);
      });

      // Store new circles
      currentCirclesRef.current = circles;
      console.log(`Visualized ${circles.length} precomputed points.`);
    },
    [googleMapRef]
  );

  // Define parseCsvFile first so it can be referenced by other functions
  const parseCsvFile = useCallback((csvFile: File) => {
    setIsParsing(true);
    setParseError(null);
    toast.info("Parsing CSV file...");

    Papa.parse<Record<string, any>>(csvFile, {
      header: true, // Assume first row is header
      skipEmptyLines: true,
      dynamicTyping: true, // Automatically convert numbers
      complete: (results: ParseResult<Record<string, any>>) => {
        setIsParsing(false);
        if (results.errors.length > 0) {
          console.error("CSV Parsing Errors:", results.errors);
          const errorMessages = results.errors
            .map((e: ParseError) => `Row ${e.row}: ${e.message}`)
            .join("; ");
          setParseError(`Error parsing CSV: ${errorMessages}`);
          toast.error("Error parsing CSV file. Check console for details.");
          setParsedPoints([]);
          return;
        }

        const points: PrecomputedPoint[] = [];
        let validationError: string | null = null;

        // Try to auto-detect common header names
        const headers = results.meta.fields;
        const latHeader = headers?.find((h: string) => /lat/i.test(h));
        const lngHeader = headers?.find((h: string) => /lon|lng/i.test(h));
        const radiusHeader = headers?.find((h: string) =>
          /radius|rad/i.test(h)
        );

        if (!latHeader || !lngHeader || !radiusHeader) {
          validationError =
            "Could not automatically detect 'latitude', 'longitude', and 'radius' columns in the CSV header. Please ensure these columns exist.";
        } else {
          console.log(
            `Detected headers: Lat=${latHeader}, Lng=${lngHeader}, Radius=${radiusHeader}`
          );
          results.data.forEach((row: Record<string, any>, index: number) => {
            const lat = row[latHeader];
            const lng = row[lngHeader];
            const radius = row[radiusHeader];

            // Validate data types and ranges (WGS84)
            if (
              typeof lat === "number" &&
              isFinite(lat) &&
              lat >= -90 &&
              lat <= 90 &&
              typeof lng === "number" &&
              isFinite(lng) &&
              lng >= -180 &&
              lng <= 180 &&
              typeof radius === "number" &&
              isFinite(radius) &&
              radius > 0
            ) {
              points.push({ lat, lng, radius });
            } else {
              // Collect first validation error
              if (!validationError) {
                validationError = `Invalid data in row ${
                  index + 1
                }. Check latitude (-90 to 90), longitude (-180 to 180), and radius (> 0). Found: lat=${lat}, lng=${lng}, radius=${radius}`;
              }
            }
          });
        }

        if (validationError) {
          setParseError(validationError);
          toast.error(validationError);
          setParsedPoints([]);
        } else if (points.length === 0) {
          setParseError("No valid data points found in the CSV file.");
          toast.warning("No valid data points found in the CSV file.");
          setParsedPoints([]);
          setOriginalPoints([]);
        } else {
          setParsedPoints(points);
          setOriginalPoints(points); // Save original points for polygon filtering
          toast.success(
            `Successfully parsed ${points.length} points from the CSV.`
          );
        }
      },
      error: (error: Error) => {
        setIsParsing(false);
        console.error("CSV Parsing Failed:", error);
        setParseError(`Failed to parse CSV: ${error.message}`);
        toast.error("Failed to parse CSV file.");
        setParsedPoints([]);
      },
    });
  }, []);

  // Fetch preloaded CSV files
  const fetchPreloadedFiles = useCallback(async () => {
    setIsLoadingFiles(true);
    try {
      const response = await fetch("/api/precomputed-csv");
      if (!response.ok) {
        throw new Error("Failed to fetch preloaded CSV files");
      }
      const data = await response.json();
      setPreloadedFiles(data.files || []);
    } catch (error) {
      console.error("Error fetching preloaded CSV files:", error);
      toast.error("Failed to load preloaded CSV files");
      setPreloadedFiles([]);
    } finally {
      setIsLoadingFiles(false);
    }
  }, []);

  // Utility functions for saving and loading search state
  const STORAGE_KEY = "precomputed_lead_search_state";

  // Toggle debug panel visibility
  const toggleDebugPanel = useCallback(() => {
    setShowDebugPanel((prev) => !prev);
  }, []);

  // Save search state to localStorage
  const saveSearchState = useCallback(
    (
      points: PrecomputedPoint[],
      partialResults: SearchResultPoint[],
      resultsCount: number[],
      progress: number,
      searchType: string,
      searchKeyword?: string
    ) => {
      try {
        setIsSavingResults(true);

        const state: SavedSearchState = {
          timestamp: Date.now(),
          points,
          partialResults,
          resultsCountPerPoint: resultsCount,
          searchProgress: progress,
          searchType,
          searchKeyword,
        };

        localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
        setSavedSearchState(state);
        console.log(
          `Saved search state with ${partialResults.length} results at ${progress}% completion`
        );

        setIsSavingResults(false);
        return true;
      } catch (error) {
        console.error("Error saving search state:", error);
        setIsSavingResults(false);
        return false;
      }
    },
    []
  );

  // Load search state from localStorage
  const loadSearchState = useCallback(() => {
    try {
      const savedStateJson = localStorage.getItem(STORAGE_KEY);
      if (!savedStateJson) return null;

      const savedState: SavedSearchState = JSON.parse(savedStateJson);

      // Check if the saved state is recent enough (within the last 24 hours)
      const isRecent = Date.now() - savedState.timestamp < 24 * 60 * 60 * 1000;

      if (isRecent && savedState.partialResults.length > 0) {
        console.log(
          `Loaded saved search state with ${savedState.partialResults.length} results`
        );
        return savedState;
      } else {
        // Clear old saved state
        localStorage.removeItem(STORAGE_KEY);
        return null;
      }
    } catch (error) {
      console.error("Error loading search state:", error);
      return null;
    }
  }, []);

  // Clear saved search state
  const clearSearchState = useCallback(() => {
    localStorage.removeItem(STORAGE_KEY);
    setSavedSearchState(null);
    setIsRecoveryAvailable(false);
    setSearchProgress(0);
  }, []);

  // Check for saved search state on component mount
  useEffect(() => {
    const savedState = loadSearchState();
    if (savedState) {
      setSavedSearchState(savedState);
      setIsRecoveryAvailable(true);
      setSearchProgress(savedState.searchProgress);
      toast.info(
        `Found saved search with ${savedState.partialResults.length} results. You can continue from where you left off.`
      );
    }
  }, [loadSearchState]);

  // Load preloaded CSV files on component mount
  useEffect(() => {
    fetchPreloadedFiles();
  }, [fetchPreloadedFiles]);

  // Handle preloaded CSV file selection
  const handlePreloadedFileSelect = useCallback(
    async (filePath: string) => {
      setIsParsing(true);
      setParseError(null);
      setParsedPoints([]);

      try {
        // Clear any existing polygon
        if (selectedArea) {
          selectedArea.setMap(null);
          setSelectedArea(null);
        }

        // Reset original points
        setOriginalPoints([]);

        const response = await fetch(filePath);
        if (!response.ok) {
          throw new Error(`Failed to fetch file: ${filePath}`);
        }

        const csvText = await response.text();
        const csvBlob = new Blob([csvText], { type: "text/csv" });
        const csvFile = new File(
          [csvBlob],
          filePath.split("/").pop() || "preloaded.csv",
          { type: "text/csv" }
        );

        setFile(csvFile);
        parseCsvFile(csvFile);
      } catch (error) {
        console.error("Error loading preloaded CSV file:", error);
        toast.error("Failed to load the selected CSV file");
        setIsParsing(false);
        setParseError("Failed to load the selected CSV file");
      }
    },
    [parseCsvFile]
  );

  // Load Google Maps script (similar to ExtensiveLeadGenerator)
  useEffect(() => {
    if (typeof window === "undefined") return;

    const loadGoogleMapsScript = () => {
      const scriptId = "google-maps-script"; // Use same ID
      const existingScript = document.getElementById(scriptId);

      // Define the callback function if it doesn't exist
      if (!(window as any).initMap) {
        (window as any).initMap = () => {
          console.log("Google Maps API loaded by PrecomputedLeadGenerator");
          // Note: This might be called multiple times if ExtensiveLeadGenerator also loads it.
          // Setting state here ensures this component knows the map is ready.
          setMapLoaded(true);
          // Clean up the global function after it's called to avoid conflicts
          // It might be better to use a Promise-based approach or a shared context for script loading
          // For now, immediate cleanup might work if load order is consistent
          // setTimeout(() => { (window as any).initMap = undefined; }, 0);
        };
      }

      if (!existingScript) {
        const script = document.createElement("script");
        script.id = scriptId;
        script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=geometry,drawing&callback=initMap`; // Include both geometry and drawing libraries
        script.async = true;
        script.defer = true;
        script.onerror = () => {
          console.error("Failed to load Google Maps API script");
          toast.error("Failed to load Google Maps. Please check connection.");
          // Potentially remove the callback if script fails
          // (window as any).initMap = undefined;
        };
        document.head.appendChild(script);
        console.log("Appending Google Maps script...");
      } else if (window.google && window.google.maps) {
        console.log(
          "Google Maps script already exists. Triggering map load state."
        );
        setMapLoaded(true); // If script exists and API is loaded, set state
      }
    };

    loadGoogleMapsScript();

    // Expose the visualizeSearchResultPoints function to the window object
    window.visualizeSearchResultsOnMap = visualizeSearchResultPoints;
    console.log(
      "Exposed visualizeSearchResultsOnMap to window object from PrecomputedLeadGenerator"
    );

    return () => {
      // Clean up global callbacks
      if (typeof window !== "undefined") {
        if ("visualizeSearchResultsOnMap" in window) {
          (window as any).visualizeSearchResultsOnMap = undefined;
          console.log(
            "Cleaned up visualizeSearchResultsOnMap from window object"
          );
        }
      }
    };
  }, [visualizeSearchResultPoints]);

  // Initialize map when script is loaded
  useEffect(() => {
    if (!mapLoaded || !mapRef.current || !window.google || !window.google.maps)
      return;

    console.log("Initializing Precomputed map...");
    try {
      const defaultCenter = { lat: 46.8182, lng: 8.2275 }; // Switzerland center
      const map = new google.maps.Map(mapRef.current, {
        center: defaultCenter,
        zoom: 8,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: false,
      });
      googleMapRef.current = map;
      console.log("Precomputed map initialized.");
    } catch (error) {
      console.error("Error initializing Precomputed Google Map:", error);
      toast.error("Failed to initialize the map.");
    }

    // Cleanup map instance on component unmount
    return () => {
      console.log("Cleaning up Precomputed map instance.");
      googleMapRef.current = null;
      // Note: Circles cleanup is handled by handleClearMap or visualize function
    };
  }, [mapLoaded]);

  // Helper function to check if a point is inside a polygon
  const isPointInPolygon = useCallback(
    (
      point: { lat: number; lng: number },
      polygon: google.maps.Polygon
    ): boolean => {
      if (!polygon) return false;

      // Convert point to LatLng
      const latLng = new google.maps.LatLng(point.lat, point.lng);

      // Use Google Maps containsLocation function if available
      if (
        google.maps.geometry &&
        google.maps.geometry.poly &&
        google.maps.geometry.poly.containsLocation
      ) {
        return google.maps.geometry.poly.containsLocation(latLng, polygon);
      }

      // Fallback to manual calculation using bounds first as a quick check
      const bounds = new google.maps.LatLngBounds();
      polygon.getPath().forEach((vertex) => bounds.extend(vertex));

      if (!bounds.contains(latLng)) {
        return false; // Outside bounds, definitely not in polygon
      }

      // Ray casting algorithm for point-in-polygon test
      let isInside = false;
      const path = polygon.getPath();
      for (let i = 0, j = path.getLength() - 1; i < path.getLength(); j = i++) {
        const xi = path.getAt(i).lat();
        const yi = path.getAt(i).lng();
        const xj = path.getAt(j).lat();
        const yj = path.getAt(j).lng();

        const intersect =
          yi > point.lng !== yj > point.lng &&
          point.lat < ((xj - xi) * (point.lng - yi)) / (yj - yi) + xi;

        if (intersect) isInside = !isInside;
      }

      return isInside;
    },
    []
  );

  // Function to filter points based on polygon
  const filterPointsByPolygon = useCallback(
    (
      points: PrecomputedPoint[],
      polygon: google.maps.Polygon
    ): PrecomputedPoint[] => {
      if (!polygon) return points;

      return points.filter((point) => isPointInPolygon(point, polygon));
    },
    [isPointInPolygon]
  );

  // Store the original points before filtering
  const [originalPoints, setOriginalPoints] = useState<PrecomputedPoint[]>([]);

  // Function to update filtered points when polygon changes
  const updateFilteredPoints = useCallback(
    (polygon: google.maps.Polygon) => {
      if (!polygon || originalPoints.length === 0) return;

      // Filter points based on polygon
      const filteredPoints = filterPointsByPolygon(originalPoints, polygon);

      // Update the points and visualize them
      if (filteredPoints.length > 0) {
        setParsedPoints(filteredPoints);
        toast.success(
          `Selected ${filteredPoints.length} points inside the polygon`
        );
      } else {
        toast.warning("No points found inside the polygon");
      }
    },
    [originalPoints, filterPointsByPolygon]
  );

  // Function to handle polygon path changes (when user edits the polygon)
  const setupPolygonEditListeners = useCallback(
    (polygon: google.maps.Polygon) => {
      // Add listeners for when the polygon is edited
      const path = polygon.getPath();

      // Listen for any changes to the polygon path
      google.maps.event.addListener(path, "set_at", () => {
        updateFilteredPoints(polygon);
      });

      google.maps.event.addListener(path, "insert_at", () => {
        updateFilteredPoints(polygon);
      });

      google.maps.event.addListener(path, "remove_at", () => {
        updateFilteredPoints(polygon);
      });
    },
    [updateFilteredPoints]
  );

  // Function to start polygon drawing mode
  const startPolygonDrawing = useCallback(() => {
    if (!googleMapRef.current) return;

    // Save original points if not already saved
    if (originalPoints.length === 0) {
      setOriginalPoints([...parsedPoints]);
    }

    // Clear any existing polygon
    if (selectedArea) {
      selectedArea.setMap(null);
      setSelectedArea(null);
    }

    setIsOptimizingArea(true);
    toast.info(
      "Draw a polygon to select the search area. You can edit the polygon after drawing."
    );

    // Initialize drawing manager if not already done
    if (!drawingManagerRef.current) {
      const drawingManager = new google.maps.drawing.DrawingManager({
        drawingMode: google.maps.drawing.OverlayType.POLYGON,
        drawingControl: true,
        drawingControlOptions: {
          position: google.maps.ControlPosition.TOP_CENTER,
          drawingModes: [google.maps.drawing.OverlayType.POLYGON],
        },
        polygonOptions: {
          editable: true,
          fillColor: "#3b82f6",
          fillOpacity: 0.3,
          strokeColor: "#1d4ed8",
          strokeWeight: 2,
        },
      });

      drawingManager.setMap(googleMapRef.current);
      drawingManagerRef.current = drawingManager;

      // Add event listener for polygon complete
      google.maps.event.addListener(
        drawingManager,
        "polygoncomplete",
        function polygonCompleteHandler(polygon: google.maps.Polygon) {
          // Only allow one polygon at a time
          if (selectedArea) {
            selectedArea.setMap(null);
          }

          // Make sure the polygon stays on the map
          polygon.setMap(googleMapRef.current);
          setSelectedArea(polygon);

          // Disable drawing mode
          if (drawingManagerRef.current) {
            drawingManagerRef.current.setDrawingMode(null);
          }

          // Set up listeners for polygon edits
          setupPolygonEditListeners(polygon);

          // Initial filtering of points
          updateFilteredPoints(polygon);

          setIsOptimizingArea(false);

          // Add a tooltip to inform users they can edit the polygon
          const infoWindow = new google.maps.InfoWindow({
            content: `<div style="padding: 8px; max-width: 200px;">
              <strong>Polygon Selection Active</strong><br>
              You can edit this polygon by dragging the vertices.<br>
              Points will update automatically.
            </div>`,
          });

          // Show the info window briefly
          if (googleMapRef.current) {
            infoWindow.setPosition(polygon.getPath().getAt(0));
            infoWindow.open({
              map: googleMapRef.current,
            });

            // Close after 5 seconds
            setTimeout(() => {
              infoWindow.close();
            }, 5000);
          }
        }
      );
    } else {
      // Just set the drawing mode if manager already exists
      drawingManagerRef.current.setDrawingMode(
        google.maps.drawing.OverlayType.POLYGON
      );
      drawingManagerRef.current.setMap(googleMapRef.current);
    }
  }, [
    googleMapRef,
    selectedArea,
    parsedPoints,
    originalPoints,
    setupPolygonEditListeners,
    updateFilteredPoints,
  ]);

  // Visualize points when parsedPoints state updates
  useEffect(() => {
    if (parsedPoints.length > 0 && googleMapRef.current) {
      visualizePrecomputedPoints(parsedPoints);
      // Adjust map view to fit points
      adjustMapView(parsedPoints);
    }
    // If parsedPoints becomes empty, clear the map
    else if (
      parsedPoints.length === 0 &&
      googleMapRef.current &&
      currentCirclesRef.current.length > 0
    ) {
      clearMapVisualization();
    }
  }, [parsedPoints, visualizePrecomputedPoints, adjustMapView]); // Dependencies are correct

  // Store the current map state before search
  const [preserveMapView, setPreserveMapView] = useState(false);
  const [initialMapState, setInitialMapState] = useState<{
    center?: google.maps.LatLng;
    zoom?: number;
    bounds?: google.maps.LatLngBounds;
  }>({});

  // Visualize search results when they change
  useEffect(() => {
    if (searchResults.length > 0 && googleMapRef.current) {
      visualizeSearchResultPoints(searchResults);

      // Only adjust the map view if we're not preserving the current view
      if (!preserveMapView) {
        // If we have both search points and parsed points, adjust the map view to include all
        if (parsedPoints.length > 0) {
          const allPoints = [
            ...parsedPoints.map((p) => ({ lat: p.lat, lng: p.lng })),
            ...searchResults,
          ];
          adjustMapView(allPoints);
        } else {
          // Otherwise just adjust to the search results
          adjustMapView(searchResults);
        }
      } else if (
        initialMapState.center &&
        initialMapState.zoom &&
        googleMapRef.current
      ) {
        // Restore the initial map view
        googleMapRef.current.setCenter(initialMapState.center);
        googleMapRef.current.setZoom(initialMapState.zoom);
      }
    }
  }, [
    searchResults,
    googleMapRef,
    visualizeSearchResultPoints,
    parsedPoints,
    adjustMapView,
    preserveMapView,
    initialMapState,
  ]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (
        selectedFile.type === "text/csv" ||
        selectedFile.name.endsWith(".csv")
      ) {
        setFile(selectedFile);
        setParsedPoints([]); // Clear previous results
        setParseError(null); // Clear previous errors
        parseCsvFile(selectedFile);
      } else {
        toast.error("Please upload a valid CSV file.");
        setFile(null);
        setParsedPoints([]);
        setParseError("Invalid file type. Only CSV files are accepted.");
      }
    }
  };

  // Clear map when file input is cleared or changed
  const handleClearMapClick = () => {
    if (googleMapRef.current) {
      clearMapVisualization();
      clearResultMarkers();
      // Reset map view
      googleMapRef.current.setCenter({ lat: 46.8182, lng: 8.2275 });
      googleMapRef.current.setZoom(8);
    }

    // Reset map view preservation
    setPreserveMapView(false);
    setInitialMapState({});

    // Clear all data
    setSearchResults([]);
    setParsedPoints([]);
    setOriginalPoints([]);
    setParseError(null);
    setFile(null);

    // If there's a selected polygon, clear it
    if (selectedArea) {
      selectedArea.setMap(null);
      setSelectedArea(null);
    }

    // If there's a drawing manager, reset it
    if (drawingManagerRef.current) {
      drawingManagerRef.current.setMap(null);
    }

    // Reset optimization state
    setIsOptimizingArea(false);

    // Reset file input
    const fileInput = document.getElementById("csv-upload") as HTMLInputElement;
    if (fileInput) fileInput.value = "";

    toast.info("Map cleared");
  };

  const handleStartPrecomputedSearch = async () => {
    if (parsedPoints.length === 0) {
      toast.error("No valid points parsed from the CSV file to search.");
      return;
    }

    // If we have a saved search state and recovery is available, ask user if they want to continue
    if (isRecoveryAvailable && savedSearchState) {
      // Continue with saved search - we'll handle this in the UI
      return;
    }

    // Capture the current map state before starting the search
    if (googleMapRef.current) {
      const map = googleMapRef.current;
      setInitialMapState({
        center: map.getCenter(),
        zoom: map.getZoom(),
        // Use type assertion for getBounds as it exists on Google Maps API
        bounds: (map as any).getBounds() || undefined,
      });
      setPreserveMapView(true);
      console.log("Captured initial map state to preserve view during search");
    }

    // Clear any existing search results before starting a new search
    setSearchResults([]);
    clearResultMarkers();
    clearSearchState(); // Clear any previous saved state

    // Reset statistics
    setMaxResultsCount(0);
    setResultsCountPerPoint([]);
    setAverageResultsPerPoint(0);
    setSearchProgress(0);

    // Create placeholder results array to show the summary immediately
    const placeholderResults: SearchResultPoint[] = [
      {
        place_id: "placeholder_1",
        name: "Searching...",
        lat: 0,
        lng: 0,
      },
    ];
    setSearchResults(placeholderResults);

    // Log that we're showing the placeholder results
    console.log("Showing placeholder results to display the summary section");

    // Reset API progress tracking
    setApiCallsTotal(parsedPoints.length);
    setApiCallsCompleted(0);
    setApiCallsFailed(0);
    setApiCallsInProgress(1); // Start with 1 in progress

    // Declare and initialize interval variables at the function scope so they can be accessed in catch block
    let progressInterval: NodeJS.Timeout | undefined = undefined;
    let apiProgressInterval: NodeJS.Timeout | undefined = undefined;

    try {
      // Get search parameters from parent component context
      const searchType = "places"; // Default value, ideally this would come from context
      const searchKeyword = ""; // Default value, ideally this would come from context

      // Initialize partial results array to store results as they come in
      let partialResults: SearchResultPoint[] = [];
      let partialResultsCount: number[] = [];

      // Save initial state with 0% progress
      saveSearchState(
        parsedPoints,
        partialResults,
        partialResultsCount,
        0,
        searchType,
        searchKeyword
      );

      // Set up a more frequent progress update interval
      progressInterval = setInterval(() => {
        // Check localStorage for any updates from the API
        const currentState = loadSearchState();
        if (currentState) {
          // Update progress state
          setSearchProgress(currentState.searchProgress);

          // Update API call stats based on progress
          const completedCalls = Math.floor(
            (currentState.searchProgress / 100) * parsedPoints.length
          );
          setApiCallsCompleted(completedCalls);

          // Calculate in-progress calls - more accurate estimation
          const inProgressCalls = Math.min(
            3,
            parsedPoints.length - completedCalls
          );
          setApiCallsInProgress(inProgressCalls);

          // Update partial results if available
          if (
            currentState.partialResults &&
            currentState.partialResults.length > 0
          ) {
            setSearchResults(currentState.partialResults);

            // Visualize partial results on the map as they come in
            if (currentState.partialResults.length % 10 === 0) {
              // Update map every 10 new results
              visualizeSearchResultPoints(currentState.partialResults);
            }
          }

          // Log progress updates to console for debugging
          console.log(
            `Progress update: ${currentState.searchProgress}%, Completed: ${completedCalls}, In Progress: ${inProgressCalls}`
          );
        }
      }, 100); // More frequent updates (100ms instead of 200ms)

      // Set up a separate polling mechanism to check API progress directly
      let progressCheckCount = 0;
      const searchId = `search_${Date.now()}`; // Create a consistent search ID for this session
      console.log(`Starting API progress polling with searchId: ${searchId}`);

      // Initial progress check to establish baseline
      const checkProgress = async () => {
        try {
          console.log(
            `Checking API progress (attempt ${progressCheckCount})...`
          );
          const response = await fetch("/api/places/progress-check", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              searchId: "default_search", // Use the default search ID that the API is using
            }),
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`API progress response:`, data);

            if (data.progress !== undefined) {
              // Update progress state directly from API response
              setSearchProgress(data.progress);

              // Update API call stats based on progress from API
              const completedCalls =
                data.completed ||
                Math.floor((data.progress / 100) * parsedPoints.length);
              setApiCallsCompleted(completedCalls);

              // Update in-progress calls
              setApiCallsInProgress(
                data.inProgress ||
                  Math.min(3, parsedPoints.length - completedCalls)
              );

              console.log(
                `API progress check: ${data.progress}%, API calls completed: ${completedCalls}/${parsedPoints.length}`
              );

              // If we have a high progress value, update more frequently
              if (data.progress > 0 && data.progress < 100) {
                // We're making progress, so keep checking frequently
                console.log(
                  `Progress is ${data.progress}%, continuing to poll frequently`
                );
              }

              return data.progress;
            } else {
              console.log(`API progress response missing progress data:`, data);
              return 0;
            }
          } else {
            console.error(
              `API progress check failed with status: ${response.status}`
            );
            return 0;
          }
        } catch (error) {
          console.error("Error checking API progress:", error);
          return 0;
        }
      };

      // Set initial progress to 5% to show something is happening
      setSearchProgress(5);
      setApiCallsCompleted(0);
      setApiCallsInProgress(Math.min(3, parsedPoints.length));

      // Do an immediate check
      checkProgress();

      // Then set up the interval
      apiProgressInterval = setInterval(async () => {
        progressCheckCount++;
        const progress = await checkProgress();

        // If progress is 100%, we can stop polling
        if (progress === 100) {
          console.log("Search completed (progress 100%), stopping polling");
          if (apiProgressInterval) {
            clearInterval(apiProgressInterval);
            apiProgressInterval = undefined;
          }
        }

        // Log every few checks for debugging
        if (progressCheckCount % 10 === 0) {
          console.log(
            `Progress check #${progressCheckCount}: Current progress = ${progress}%`
          );
        }
      }, 200); // Check more frequently (every 200ms)

      // Call the search function and wait for results
      const response = await onStartSearch(parsedPoints);

      // Clear the intervals when done
      clearInterval(progressInterval);
      clearInterval(apiProgressInterval);

      // Extract results and counts
      const { results, resultsCountPerPoint } = response;

      // Update state with the search results
      setSearchResults(results);
      partialResults = results;

      // Update API progress tracking
      setApiCallsCompleted(parsedPoints.length);
      setApiCallsInProgress(0);
      setSearchProgress(100);

      // Process statistics if we have results counts per point
      if (resultsCountPerPoint && resultsCountPerPoint.length > 0) {
        // Store the results count per point
        setResultsCountPerPoint(resultsCountPerPoint);
        partialResultsCount = resultsCountPerPoint;

        // Find the maximum results count
        const maxCount = Math.max(...resultsCountPerPoint);
        setMaxResultsCount(maxCount);

        // Calculate the average
        const totalResults = resultsCountPerPoint.reduce(
          (sum, count) => sum + count,
          0
        );
        const average = totalResults / resultsCountPerPoint.length;
        setAverageResultsPerPoint(average);

        // Log statistics to console
        console.log("=== SEARCH RESULTS STATISTICS ===");
        console.log(`Total points searched: ${resultsCountPerPoint.length}`);
        console.log(`Maximum results for any point: ${maxCount}`);
        console.log(`Average results per point: ${average.toFixed(2)}`);
        console.log("Results per point:", resultsCountPerPoint);
      }

      // Save final state with 100% progress
      saveSearchState(
        parsedPoints,
        partialResults,
        partialResultsCount,
        100,
        searchType,
        searchKeyword
      );

      // Clear saved state since search completed successfully
      clearSearchState();

      // Display the results as points on the map
      if (results.length > 0) {
        visualizeSearchResultPoints(results);
        toast.success(
          `Displaying ${results.length} search result points on the map.`
        );
      } else {
        toast.info("No results found with valid coordinates.");
      }

      // Keep the map view preserved for this search session
      // We don't reset preserveMapView here to maintain the initial view
    } catch (error) {
      console.error("Error handling search results:", error);
      toast.error(
        "Failed to process search results. Any partial results have been saved and can be recovered."
      );

      // Clear the intervals on error if they exist
      if (typeof progressInterval !== "undefined") {
        clearInterval(progressInterval);
      }
      if (typeof apiProgressInterval !== "undefined") {
        clearInterval(apiProgressInterval);
      }

      // Update API progress tracking for failure
      setApiCallsFailed(apiCallsFailed + 1);
      setApiCallsInProgress(0);

      // If we have partial results, save them
      if (searchResults.length > 0) {
        const progressPercent = Math.min(
          90,
          Math.round((searchResults.length / parsedPoints.length) * 100)
        );

        saveSearchState(
          parsedPoints,
          searchResults,
          resultsCountPerPoint,
          progressPercent,
          "places",
          ""
        );
        setIsRecoveryAvailable(true);
        setSearchProgress(progressPercent);
      }
    }
  };

  // Function to continue from saved search state
  const handleContinueFromSavedSearch = useCallback(() => {
    if (!savedSearchState) {
      toast.error("No saved search state available.");
      return;
    }

    // Restore the saved state
    setParsedPoints(savedSearchState.points);
    setSearchResults(savedSearchState.partialResults);
    setResultsCountPerPoint(savedSearchState.resultsCountPerPoint);
    setSearchProgress(savedSearchState.searchProgress);

    // Update API progress tracking
    const totalPoints = savedSearchState.points.length;
    const completedPoints = Math.floor(
      (savedSearchState.searchProgress / 100) * totalPoints
    );

    setApiCallsTotal(totalPoints);
    setApiCallsCompleted(completedPoints);
    setApiCallsInProgress(0);
    setApiCallsFailed(0);

    // Calculate statistics from saved state
    if (savedSearchState.resultsCountPerPoint.length > 0) {
      const maxCount = Math.max(...savedSearchState.resultsCountPerPoint);
      setMaxResultsCount(maxCount);

      const totalResults = savedSearchState.resultsCountPerPoint.reduce(
        (sum, count) => sum + count,
        0
      );
      const average =
        totalResults / savedSearchState.resultsCountPerPoint.length;
      setAverageResultsPerPoint(average);
    }

    // Visualize the points on the map
    visualizePrecomputedPoints(savedSearchState.points);

    // Visualize the results on the map
    if (savedSearchState.partialResults.length > 0) {
      visualizeSearchResultPoints(savedSearchState.partialResults);
      toast.success(
        `Restored ${savedSearchState.partialResults.length} search result points from saved state (${savedSearchState.searchProgress}% complete).`
      );
    }

    // Show debug panel to display localStorage data
    setShowDebugPanel(true);

    // Don't clear the saved state immediately - keep it for reference
    // We'll continue the search from where it left off
    setIsRecoveryAvailable(false); // Hide recovery UI since we're now continuing

    // Reset map view preservation since we're explicitly adjusting the view for the saved search
    setPreserveMapView(false);
  }, [
    savedSearchState,
    visualizePrecomputedPoints,
    visualizeSearchResultPoints,
    setPreserveMapView,
  ]);

  return (
    <Card className="bg-green-50/50">
      <CardContent className="p-4 grid gap-4">
        <Tabs
          value={selectedTab}
          onValueChange={setSelectedTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="preloaded">Preloaded CSV Files</TabsTrigger>
            <TabsTrigger value="upload">Upload CSV File</TabsTrigger>
          </TabsList>

          <TabsContent value="preloaded" className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">
                  Select a preloaded CSV file
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => fetchPreloadedFiles()}
                  disabled={isLoadingFiles}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw
                    className={`h-4 w-4 ${
                      isLoadingFiles ? "animate-spin" : ""
                    }`}
                  />
                  <span className="sr-only">Refresh</span>
                </Button>
              </div>

              {isLoadingFiles ? (
                <div className="flex items-center justify-center p-4">
                  <Loader className="h-5 w-5 animate-spin mr-2" />
                  <span>Loading files...</span>
                </div>
              ) : preloadedFiles.length === 0 ? (
                <div className="text-center p-4 border rounded-md bg-gray-50">
                  <p className="text-sm text-gray-500">
                    No preloaded CSV files found.
                  </p>
                </div>
              ) : (
                <div className="grid gap-2">
                  {preloadedFiles.map((file) => (
                    <Button
                      key={file.path}
                      variant="outline"
                      className="flex justify-between items-center w-full h-auto py-3 px-4"
                      onClick={() => handlePreloadedFileSelect(file.path)}
                      disabled={isParsing || loading}
                    >
                      <div className="flex items-center">
                        <FileIcon className="h-4 w-4 mr-2 text-blue-500" />
                        <span className="font-medium">{file.displayName}</span>
                      </div>
                      <span className="text-xs text-gray-500">.csv</span>
                    </Button>
                  ))}
                </div>
              )}

              <p className="text-xs text-muted-foreground mt-2">
                Select a preloaded CSV file to use for lead generation. Files
                must contain columns for latitude, longitude, and radius.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="upload" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="csv-upload">Upload CSV File</Label>
              <Input
                id="csv-upload"
                type="file"
                accept=".csv"
                onChange={handleFileChange}
                disabled={isParsing || loading}
              />
              <p className="text-xs text-muted-foreground">
                CSV must contain columns for latitude, longitude, and radius
                (meters). Common headers like &apos;lat&apos;, &apos;lon&apos;,
                &apos;lng&apos;, &apos;radius&apos; are auto-detected.
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {/* Map Container with increased height (30% taller) */}
        <div className="h-[650px] w-full relative mt-4">
          {!mapLoaded && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-80 z-10">
              <Loader className="w-8 h-8 animate-spin text-blue-500" />
              <span className="ml-2">Loading map...</span>
            </div>
          )}
          <div
            ref={mapRef}
            className="h-full w-full rounded-md border border-gray-200"
          />
        </div>

        {parseError && (
          <div className="text-red-600 text-sm p-2 bg-red-100 rounded-md">
            {parseError}
          </div>
        )}

        {parsedPoints.length > 0 && !isParsing && (
          <div className="bg-white p-3 rounded-md border space-y-2">
            <h4 className="font-medium text-sm">Parsed Points Summary</h4>
            <p className="text-sm text-green-700">
              Successfully parsed {parsedPoints.length} points.
            </p>

            {/* Cost Estimation */}
            <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
              <h5 className="font-medium text-sm text-blue-800 mb-2">
                💰 Estimated API Cost
              </h5>
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-700">
                  {parsedPoints.length} search points × 0.03 CHF
                </span>
                <span className="text-lg font-bold text-blue-900">
                  {(parsedPoints.length * 0.03).toFixed(2)} CHF
                </span>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                Cost estimate based on Google Places API pricing. Actual cost
                may vary.
              </p>
            </div>

            {/* Optionally show a preview of the first few points */}
            {/* <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-24">
              {JSON.stringify(parsedPoints.slice(0, 5), null, 2)}
            </pre> */}
          </div>
        )}

        {/* Recovery UI */}
        {isRecoveryAvailable && savedSearchState && (
          <div className="bg-yellow-50 p-3 rounded-md border border-yellow-200 space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm text-yellow-800">
                Saved Search Available
              </h4>
              <span className="text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded">
                {savedSearchState.searchProgress}% Complete
              </span>
            </div>
            <p className="text-sm text-yellow-700">
              Found a saved search with {savedSearchState.partialResults.length}{" "}
              results from{" "}
              {new Date(savedSearchState.timestamp).toLocaleString()}.
            </p>
            <div className="flex space-x-2 pt-1">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 border-yellow-300 hover:bg-yellow-100"
                onClick={clearSearchState}
              >
                Discard
              </Button>
              <Button
                variant="default"
                size="sm"
                className="flex-1 bg-yellow-500 hover:bg-yellow-600"
                onClick={handleContinueFromSavedSearch}
              >
                Continue Search
              </Button>
            </div>
          </div>
        )}

        {/* API Progress Display */}
        {(loading || searchResults.length > 0 || searchProgress > 0) && (
          <ApiProgressDisplay
            progress={searchProgress}
            apiCallsTotal={apiCallsTotal}
            apiCallsCompleted={apiCallsCompleted}
            apiCallsFailed={apiCallsFailed}
            apiCallsInProgress={apiCallsInProgress}
            showDebugPanel={showDebugPanel}
            toggleDebugPanel={toggleDebugPanel}
            savedState={savedSearchState}
          />
        )}

        <div className="bg-white p-3 rounded-md border space-y-4 mt-2">
          <div>
            <h4 className="font-medium text-sm">Search Results Summary</h4>
            {loading ? (
              <p className="text-sm text-blue-700">
                Searching... {searchProgress}% complete
              </p>
            ) : searchResults.length > 0 &&
              searchResults[0].place_id !== "placeholder_1" ? (
              <p className="text-sm text-green-700">
                Found {searchResults.length} results with valid coordinates.
              </p>
            ) : (
              <p className="text-sm text-gray-700">
                No search results yet. Start a search to find places.
              </p>
            )}
            <p className="text-xs text-gray-600">
              Results will be displayed as red dots on the map. The full results
              table will be shown below the map.
            </p>
          </div>

          {/* Results Statistics */}
          {(resultsCountPerPoint.length > 0 || loading) && (
            <div className="pt-2 border-t border-gray-200">
              <h5 className="font-medium text-xs text-gray-700">
                Results Statistics
              </h5>
              <div className="grid grid-cols-2 gap-2 mt-1">
                <div className="bg-blue-50 p-2 rounded">
                  <p className="text-xs text-gray-600">Maximum Results</p>
                  <p className="text-sm font-semibold">
                    {loading && maxResultsCount === 0
                      ? "Calculating..."
                      : maxResultsCount}
                  </p>
                </div>
                <div className="bg-green-50 p-2 rounded">
                  <p className="text-xs text-gray-600">Average Results</p>
                  <p className="text-sm font-semibold">
                    {loading && averageResultsPerPoint === 0
                      ? "Calculating..."
                      : averageResultsPerPoint.toFixed(2)}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col space-y-2 mt-4">
          {parsedPoints.length > 0 && (
            <div className="flex justify-center mb-2 space-x-2">
              <Button
                onClick={startPolygonDrawing}
                variant="secondary"
                className="flex-1"
                disabled={isOptimizingArea || loading || isParsing}
              >
                {isOptimizingArea ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" /> Drawing
                    Polygon...
                  </>
                ) : (
                  <>
                    <MapPin className="mr-2 h-4 w-4" /> Optimize Search Area
                  </>
                )}
              </Button>

              {selectedArea && originalPoints.length > 0 && (
                <Button
                  onClick={() => {
                    // Reset to original points
                    setParsedPoints([...originalPoints]);

                    // Clear the polygon
                    if (selectedArea) {
                      selectedArea.setMap(null);
                      setSelectedArea(null);
                    }

                    // Hide the drawing manager
                    if (drawingManagerRef.current) {
                      drawingManagerRef.current.setMap(null);
                    }

                    toast.info("Restored original points");
                  }}
                  variant="outline"
                  className="flex-1"
                  disabled={isOptimizingArea || loading || isParsing}
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Reset Selection
                </Button>
              )}
            </div>
          )}

          <div className="flex space-x-2">
            <Button
              onClick={handleClearMapClick}
              disabled={currentCirclesRef.current.length === 0 && !parseError}
              variant="outline"
              className="flex-1"
            >
              Clear Map
            </Button>

            {isRecoveryAvailable && savedSearchState ? (
              <Button
                onClick={handleContinueFromSavedSearch}
                className="flex-1 bg-yellow-500 hover:bg-yellow-600 relative"
              >
                <span className="mr-2">↻</span> Continue Saved Search (
                {savedSearchState.partialResults.length} results)
              </Button>
            ) : (
              <Button
                onClick={handleStartPrecomputedSearch}
                disabled={
                  parsedPoints.length === 0 ||
                  loading ||
                  isParsing ||
                  isOptimizingArea
                }
                className="flex-1 relative overflow-hidden"
              >
                {loading ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    {searchProgress > 0
                      ? `Searching... ${searchProgress}% (${apiCallsCompleted}/${apiCallsTotal})`
                      : "Searching..."}
                    {searchProgress > 0 && (
                      <div
                        className="absolute bottom-0 left-0 h-1.5 bg-green-400"
                        style={{ width: `${searchProgress}%` }}
                      />
                    )}
                  </>
                ) : isParsing ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" /> Parsing...
                  </>
                ) : isSavingResults ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" /> Saving
                    results...
                  </>
                ) : (
                  "Start Search with Points"
                )}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Add global declaration for Google Maps
declare global {
  interface Window {
    initMap: () => void;
    visualizeSearchResultsOnMap?: (results: SearchResultPoint[]) => void;
  }
}

export default PrecomputedLeadGenerator;
