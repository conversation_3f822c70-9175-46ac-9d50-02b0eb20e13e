"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Target, Clock, Search } from "lucide-react";

interface HighYieldPoint {
  id: number;
  description: string;
  created_at: string;
  metadata: {
    latitude: number;
    longitude: number;
    radius: number;
    resultsCount: number;
    searchType: string;
    keyword?: string;
    apiType: string;
    pointIndex: number;
    totalPoints: number;
    pagesProcessed?: number;
  };
}

export default function HighYieldPointsDisplay() {
  const [highYieldPoints, setHighYieldPoints] = useState<HighYieldPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalPoints: 0,
    avgResults: 0,
    maxResults: 0,
    topSearchType: "",
  });

  useEffect(() => {
    fetchHighYieldPoints();
  }, []);

  const fetchHighYieldPoints = async () => {
    try {
      const response = await fetch("/api/logs?action=HIGH_YIELD_SEARCH_POINT&limit=100");
      const data = await response.json();
      
      const points = data.logs || [];
      setHighYieldPoints(points);
      
      // Calculate statistics
      if (points.length > 0) {
        const resultsCounts = points.map((p: HighYieldPoint) => p.metadata.resultsCount);
        const avgResults = resultsCounts.reduce((sum: number, count: number) => sum + count, 0) / resultsCounts.length;
        const maxResults = Math.max(...resultsCounts);
        
        // Find most common search type
        const searchTypes = points.map((p: HighYieldPoint) => p.metadata.searchType);
        const typeCount = searchTypes.reduce((acc: Record<string, number>, type: string) => {
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {});
        const topSearchType = Object.entries(typeCount).sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0] || "";
        
        setStats({
          totalPoints: points.length,
          avgResults: Math.round(avgResults * 10) / 10,
          maxResults,
          topSearchType,
        });
      }
    } catch (error) {
      console.error("Error fetching high-yield points:", error);
    } finally {
      setLoading(false);
    }
  };

  const getApiTypeBadge = (apiType: string) => {
    const isNew = apiType === "new_places_api";
    return (
      <Badge variant={isNew ? "default" : "secondary"} className="text-xs">
        {isNew ? "New API" : "Legacy API"}
      </Badge>
    );
  };

  const getResultsBadge = (count: number) => {
    if (count >= 50) return <Badge variant="destructive" className="text-xs">🔥 {count}</Badge>;
    if (count >= 35) return <Badge variant="default" className="text-xs">⭐ {count}</Badge>;
    return <Badge variant="secondary" className="text-xs">🎯 {count}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-xs text-muted-foreground">Total High-Yield Points</p>
                <p className="text-lg font-bold text-green-600">{stats.totalPoints}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-xs text-muted-foreground">Average Results</p>
                <p className="text-lg font-bold text-blue-600">{stats.avgResults}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-xs text-muted-foreground">Max Results</p>
                <p className="text-lg font-bold text-red-600">{stats.maxResults}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-xs text-muted-foreground">Top Search Type</p>
                <p className="text-sm font-bold text-purple-600">{stats.topSearchType || "N/A"}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* High-Yield Points Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-green-600" />
            High-Yield Search Points (20+ Results)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date/Time</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Results</TableHead>
                  <TableHead>Search Details</TableHead>
                  <TableHead>API Type</TableHead>
                  <TableHead>Radius</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      Loading high-yield points...
                    </TableCell>
                  </TableRow>
                ) : highYieldPoints.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      No high-yield search points found yet
                    </TableCell>
                  </TableRow>
                ) : (
                  highYieldPoints.map((point) => (
                    <TableRow key={point.id}>
                      <TableCell className="text-xs">
                        {format(new Date(point.created_at), "MMM dd, HH:mm")}
                      </TableCell>
                      <TableCell className="text-xs font-mono">
                        <div>
                          <div>Lat: {point.metadata.latitude}</div>
                          <div>Lng: {point.metadata.longitude}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getResultsBadge(point.metadata.resultsCount)}
                      </TableCell>
                      <TableCell className="text-xs">
                        <div>
                          <div><strong>Type:</strong> {point.metadata.searchType}</div>
                          {point.metadata.keyword && (
                            <div><strong>Keyword:</strong> {point.metadata.keyword}</div>
                          )}
                          <div className="text-muted-foreground">
                            Point {point.metadata.pointIndex} of {point.metadata.totalPoints}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getApiTypeBadge(point.metadata.apiType)}
                      </TableCell>
                      <TableCell className="text-xs">
                        {point.metadata.radius}m
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
